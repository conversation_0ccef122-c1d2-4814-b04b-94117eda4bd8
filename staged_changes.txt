diff --git a/drizzle/0002_gorgeous_la_nuit.sql b/drizzle/0002_gorgeous_la_nuit.sql
new file mode 100644
index 0000000..b0b948a
--- /dev/null
+++ b/drizzle/0002_gorgeous_la_nuit.sql
@@ -0,0 +1 @@
+ALTER TABLE "users" ADD COLUMN "role" text DEFAULT 'admin' NOT NULL;
\ No newline at end of file
diff --git a/src/app.d.ts b/src/app.d.ts
index da08e6d..fff55d7 100644
--- a/src/app.d.ts
+++ b/src/app.d.ts
@@ -3,11 +3,23 @@
 declare global {
 	namespace App {
 		// interface Error {}
-		// interface Locals {}
+		interface Locals {
+			session: {
+				session: import('better-auth').Session;
+				user: import('better-auth').User;
+			} | null;
+		}
 		// interface PageData {}
 		// interface PageState {}
 		// interface Platform {}
 	}
 }
 
+// Augment Better Auth types to include our custom role field
+declare module 'better-auth' {
+	interface User {
+		role?: 'admin' | 'contributor' | 'expert' | 'advertiser';
+	}
+}
+
 export {};
diff --git a/src/hooks.server.ts b/src/hooks.server.ts
index 60d814c..061d31a 100644
--- a/src/hooks.server.ts
+++ b/src/hooks.server.ts
@@ -2,6 +2,7 @@ import { auth } from '$lib/auth';
 import { sequence } from '@sveltejs/kit/hooks';
 import { svelteKitHandler } from 'better-auth/svelte-kit';
 import { redirect, type Handle } from '@sveltejs/kit';
+import { canAccessAdminRoutes } from '$lib/server/adminConfig';
 
 const betterAuthHandler: Handle = async ({ event, resolve }) => {
 	return svelteKitHandler({ event, resolve, auth });
@@ -13,16 +14,25 @@ const authorizationHandler: Handle = async ({ event, resolve }) => {
 	
 	// Only check session for protected routes to avoid unnecessary DB calls
 	if (routeId.startsWith('/app') || routeId.startsWith('/(auth)')) {
-		const session = await auth.api.getSession({ 
-			headers: event.request.headers 
+		const session = await auth.api.getSession({
+			headers: event.request.headers
 		});
-		
-		if (routeId.startsWith('/app') && !session) {
-			redirect(303, '/signin');
+
+		// Store session in locals for use in loaders/actions (avoids duplicate DB calls)
+		event.locals.session = session as any;
+
+		if (routeId.startsWith('/app')) {
+			if (!session) {
+				throw redirect(303, '/signin');
+			}
+			// Check if user has admin role for /app routes
+			if (!canAccessAdminRoutes((session.user as any).role || '')) {
+				throw redirect(303, '/unauthorized');
+			}
 		}
-		
+
 		if (routeId.startsWith('/(auth)') && session) {
-			redirect(303, '/app');
+			throw redirect(303, '/app');
 		}
 	}
 	
diff --git a/src/lib/auth.ts b/src/lib/auth.ts
index 82ef43c..f31d989 100644
--- a/src/lib/auth.ts
+++ b/src/lib/auth.ts
@@ -1,9 +1,11 @@
 import { betterAuth } from 'better-auth';
 import { drizzleAdapter } from 'better-auth/adapters/drizzle';
 import { emailOTP } from 'better-auth/plugins';
+import { createAuthMiddleware, APIError } from 'better-auth/api';
 import { db } from './server/db';
 import * as schema from './server/db/schema';
 import { BETTER_AUTH_SECRET, BETTER_AUTH_URL } from '$env/static/private';
+import { isAdminEmail, ADMIN_CONFIG, ADMIN_ONLY_ERROR } from './server/adminConfig';
 
 export const auth = betterAuth({
 	database: drizzleAdapter(db, { 
@@ -18,11 +20,34 @@ export const auth = betterAuth({
 			generateId: false
 		}
 	},
-	emailAndPassword: { 
-		enabled: true, 
+	emailAndPassword: {
+		enabled: true,
 		autoSignIn: true,
 		requireEmailVerification: true
 	},
+	hooks: {
+		before: createAuthMiddleware(async (ctx) => {
+			// Restrict registration to admin emails only
+			if (ctx.path === '/sign-up/email') {
+				const email = ctx.body?.email as string;
+				if (!email || !isAdminEmail(email)) {
+					throw new APIError('BAD_REQUEST', {
+						message: ADMIN_ONLY_ERROR
+					});
+				}
+				// Add admin role to the request body
+				return {
+					context: {
+						...ctx,
+						body: {
+							...ctx.body,
+							role: ADMIN_CONFIG.defaultRole
+						}
+					}
+				};
+			}
+		})
+	},
 	rateLimit: { storage: 'database' },
 	plugins: [
 		emailOTP({
diff --git a/src/lib/server/adminConfig.ts b/src/lib/server/adminConfig.ts
new file mode 100644
index 0000000..fd01034
--- /dev/null
+++ b/src/lib/server/adminConfig.ts
@@ -0,0 +1,36 @@
+// Admin configuration - centralized admin management
+// This keeps admin logic separate and easily extensible
+
+import { ADMIN_EMAILS } from '$env/static/private';
+
+// Parse admin emails from environment variable (O(1) lookup with Set)
+const ADMIN_EMAIL_SET = new Set(
+	ADMIN_EMAILS?.split(',').map((email: string) => email.trim().toLowerCase()) ?? []
+);
+
+// Startup-time guardrail: warn if no admin emails configured
+if (ADMIN_EMAIL_SET.size === 0) {
+	console.warn('[admin] ADMIN_EMAILS empty – no one can sign up');
+}
+
+export const ADMIN_CONFIG = {
+	// Default role for new registrations (will be 'admin' for allowed emails)
+	defaultRole: 'admin' as const,
+} as const;
+
+// Consistent error message for unauthorized access
+export const ADMIN_ONLY_ERROR = 'Registration is restricted to authorized administrators only.';
+
+// Helper functions
+export function isAdminEmail(email: string): boolean {
+	return ADMIN_EMAIL_SET.has(email.trim().toLowerCase());
+}
+
+export function canAccessAdminRoutes(userRole: string): boolean {
+	// For now, only admins can access /app routes
+	// Later: extend this for other roles as needed
+	return userRole === 'admin';
+}
+
+// Type definitions for future use
+export type UserRole = 'admin' | 'contributor' | 'expert' | 'advertiser';
diff --git a/src/lib/server/db/schema.ts b/src/lib/server/db/schema.ts
index d112f08..011eb95 100644
--- a/src/lib/server/db/schema.ts
+++ b/src/lib/server/db/schema.ts
@@ -9,6 +9,8 @@ export const users = pgTable('users', {
 	email: text('email').notNull().unique(),
 	emailVerified: boolean('email_verified').notNull(),
 	image: text('image'),
+	// User role for future extensibility
+	role: text('role').default('admin').notNull(), // 'admin' | 'contributor' | 'expert' | 'advertiser'
 	createdAt: timestamp('created_at').notNull(),
 	updatedAt: timestamp('updated_at').notNull()
 });
diff --git a/src/routes/(auth)/signup/+page.svelte b/src/routes/(auth)/signup/+page.svelte
index 64b9d30..80f9e60 100644
--- a/src/routes/(auth)/signup/+page.svelte
+++ b/src/routes/(auth)/signup/+page.svelte
@@ -28,7 +28,12 @@
 				});
 				
 				if (result.error) {
-					error = result.error.message ?? 'An unknown error occurred';
+					// Handle admin-only registration error
+					if (result.error.message?.includes('restricted to authorized administrators')) {
+						error = 'Registration is currently restricted to authorized administrators only. Please contact the site administrator if you need access.';
+					} else {
+						error = result.error.message ?? 'An unknown error occurred';
+					}
 				} else {
 					// Account created, now send verification OTP
 					const otpResult = await authClient.emailOtp.sendVerificationOtp({
@@ -106,6 +111,10 @@
 	<!-- Sign Up Form -->
 	<form class="mx-auto mt-10 flex max-w-md flex-col gap-4" onsubmit={handleSubmit} novalidate>
 		<h2 class="mb-4 text-2xl font-bold text-center">Create Account</h2>
+
+		<div class="mb-4 rounded-md bg-amber-50 border border-amber-200 p-3 text-sm text-amber-800">
+			<strong>Admin Access Only:</strong> Registration is currently restricted to authorized administrators.
+		</div>
 		
 		<!-- Hidden username field for accessibility -->
 		<input
diff --git a/src/routes/.well-known/appspecific/com.chrome.devtools.json/+server.ts b/src/routes/.well-known/appspecific/com.chrome.devtools.json/+server.ts
new file mode 100644
index 0000000..30ac466
--- /dev/null
+++ b/src/routes/.well-known/appspecific/com.chrome.devtools.json/+server.ts
@@ -0,0 +1,6 @@
+import { json } from '@sveltejs/kit';
+
+// Chrome DevTools endpoint - just return empty JSON to stop the 404 errors
+export async function GET() {
+	return json({});
+}
\ No newline at end of file
diff --git a/src/routes/admin-test/+page.svelte b/src/routes/admin-test/+page.svelte
new file mode 100644
index 0000000..8faa6b9
--- /dev/null
+++ b/src/routes/admin-test/+page.svelte
@@ -0,0 +1,97 @@
+<script lang="ts">
+	import { useSession } from '$lib/authClient';
+	
+	const session = useSession();
+</script>
+
+<div class="mx-auto max-w-2xl p-8">
+	<h1 class="mb-6 text-3xl font-bold">Admin System Test</h1>
+	
+	{#if $session?.data}
+		<div class="mb-6 rounded-lg bg-green-50 border border-green-200 p-4">
+			<h2 class="mb-2 text-lg font-semibold text-green-800">✅ Authentication Working</h2>
+			<div class="text-sm text-green-700">
+				<p><strong>User ID:</strong> {$session.data.user.id}</p>
+				<p><strong>Email:</strong> {$session.data.user.email}</p>
+				<p><strong>Name:</strong> {$session.data.user.name}</p>
+				<p><strong>Role:</strong> {($session.data.user as any).role ?? 'Not set'}</p>
+				<p><strong>Email Verified:</strong> {$session.data.user.emailVerified ? 'Yes' : 'No'}</p>
+			</div>
+		</div>
+
+		{#if ($session.data.user as any).role === 'admin'}
+			<div class="mb-6 rounded-lg bg-blue-50 border border-blue-200 p-4">
+				<h2 class="mb-2 text-lg font-semibold text-blue-800">🔑 Admin Access Confirmed</h2>
+				<p class="text-sm text-blue-700">
+					You have admin privileges and can access all CMS functionality.
+				</p>
+			</div>
+			
+			<div class="space-y-3">
+				<a 
+					href="/app" 
+					class="block rounded-md bg-blue-600 px-4 py-2 text-center text-white hover:bg-blue-700"
+				>
+					Go to Admin Dashboard
+				</a>
+				<a 
+					href="/app/add" 
+					class="block rounded-md border border-blue-600 px-4 py-2 text-center text-blue-600 hover:bg-blue-50"
+				>
+					Add Website
+				</a>
+				<a 
+					href="/app/manage" 
+					class="block rounded-md border border-blue-600 px-4 py-2 text-center text-blue-600 hover:bg-blue-50"
+				>
+					Manage Websites
+				</a>
+			</div>
+		{:else}
+			<div class="mb-6 rounded-lg bg-red-50 border border-red-200 p-4">
+				<h2 class="mb-2 text-lg font-semibold text-red-800">❌ No Admin Access</h2>
+				<p class="text-sm text-red-700">
+					Your account role is "{($session.data.user as any).role ?? 'Not set'}" but admin access requires "admin" role.
+				</p>
+			</div>
+		{/if}
+	{:else}
+		<div class="mb-6 rounded-lg bg-yellow-50 border border-yellow-200 p-4">
+			<h2 class="mb-2 text-lg font-semibold text-yellow-800">⚠️ Not Authenticated</h2>
+			<p class="text-sm text-yellow-700 mb-3">
+				You need to sign in to test the admin system.
+			</p>
+			<div class="space-y-2">
+				<a 
+					href="/signin" 
+					class="block rounded-md bg-yellow-600 px-4 py-2 text-center text-white hover:bg-yellow-700"
+				>
+					Sign In
+				</a>
+				<a 
+					href="/signup" 
+					class="block rounded-md border border-yellow-600 px-4 py-2 text-center text-yellow-600 hover:bg-yellow-50"
+				>
+					Create Admin Account
+				</a>
+			</div>
+		</div>
+	{/if}
+	
+	<div class="mt-8 rounded-lg bg-gray-50 border border-gray-200 p-4">
+		<h2 class="mb-2 text-lg font-semibold text-gray-800">📋 Setup Checklist</h2>
+		<ul class="text-sm text-gray-700 space-y-1">
+			<li>✅ Database migration generated and ready</li>
+			<li>⚠️ Update <code>ADMIN_EMAILS</code> in your <code>.env</code> file</li>
+			<li>🔄 Run <code>bun run db:migrate</code> to apply schema changes</li>
+			<li>🔄 Restart dev server after email change</li>
+			<li>📧 Test signup with your admin email</li>
+			<li>🔐 Verify OTP code (check console)</li>
+			<li>🎯 Access admin dashboard at <code>/app</code></li>
+		</ul>
+	</div>
+	
+	<div class="mt-6 text-center">
+		<a href="/" class="text-blue-600 hover:underline">← Back to Home</a>
+	</div>
+</div>
diff --git a/src/routes/app/add/+page.server.ts b/src/routes/app/add/+page.server.ts
index 28a3acb..046de2d 100644
--- a/src/routes/app/add/+page.server.ts
+++ b/src/routes/app/add/+page.server.ts
@@ -1,16 +1,13 @@
-import { fail, redirect } from '@sveltejs/kit';
+import { fail } from '@sveltejs/kit';
 import { db } from '$lib/server/db';
 import { websites } from '$lib/server/db/schema';
-import { auth } from '$lib/auth';
 import type { Actions } from './$types';
 
 export const actions = {
 	default: async (event) => {
-		// Check if user is authenticated
-		const session = await auth.api.getSession({ headers: event.request.headers });
-		if (!session?.user?.id) {
-			return redirect(303, '/signin');
-		}
+		// Session is guaranteed by the hook and stored in locals
+		const { session } = event.locals;
+		console.assert(session, 'Session should be guaranteed by hook for /app routes');
 
 		const data = await event.request.formData();
 		const title = data.get('title') as string;
@@ -26,7 +23,7 @@ export const actions = {
 		try {
 			// Insert website
 			await db.insert(websites).values({
-				userId: session.user.id,
+				userId: session!.user.id,
 				title,
 				tailwindVersion,
 				thumbnail,
diff --git a/src/routes/app/manage/+page.server.ts b/src/routes/app/manage/+page.server.ts
index 1d1240e..74ce74a 100644
--- a/src/routes/app/manage/+page.server.ts
+++ b/src/routes/app/manage/+page.server.ts
@@ -1,22 +1,19 @@
-import { fail, redirect } from '@sveltejs/kit';
+import { fail } from '@sveltejs/kit';
 import { db } from '$lib/server/db';
 import { websites } from '$lib/server/db/schema';
-import { auth } from '$lib/auth';
 import { eq, and } from 'drizzle-orm';
 import type { Actions, PageServerLoad } from './$types';
 
 export const load = (async (event) => {
-	// Get current user
-	const session = await auth.api.getSession({ headers: event.request.headers });
-	if (!session?.user?.id) {
-		return redirect(303, '/signin');
-	}
+	// Session is guaranteed by the hook and stored in locals
+	const { session } = event.locals;
+	console.assert(session, 'Session should be guaranteed by hook for /app routes');
 
 	// Fetch user's websites
 	const userWebsites = await db
 		.select()
 		.from(websites)
-		.where(eq(websites.userId, session.user.id))
+		.where(eq(websites.userId, session!.user.id))
 		.orderBy(websites.createdAt);
 
 	return {
@@ -26,10 +23,9 @@ export const load = (async (event) => {
 
 export const actions = {
 	toggleStatus: async (event) => {
-		const session = await auth.api.getSession({ headers: event.request.headers });
-		if (!session?.user?.id) {
-			return redirect(303, '/signin');
-		}
+		// Session is guaranteed by the hook and stored in locals
+		const { session } = event.locals;
+		console.assert(session, 'Session should be guaranteed by hook for /app routes');
 
 		const data = await event.request.formData();
 		const id = data.get('id') as string;
@@ -48,7 +44,7 @@ export const actions = {
 				.where(
 					and(
 						eq(websites.id, id),
-						eq(websites.userId, session.user.id) // Ensure user owns the website
+						eq(websites.userId, session!.user.id) // Ensure user owns the website
 					)
 				);
 
diff --git a/src/routes/email-otp-demo/+page.svelte b/src/routes/email-otp-demo/+page.svelte
index fd71639..dcda029 100644
--- a/src/routes/email-otp-demo/+page.svelte
+++ b/src/routes/email-otp-demo/+page.svelte
@@ -20,7 +20,7 @@
 		message = '';
 
 		try {
-			const { data, error } = await authClient.emailOtp.sendVerificationOtp({
+			const { error } = await authClient.emailOtp.sendVerificationOtp({
 				email,
 				type: otpType
 			});
@@ -127,7 +127,7 @@
 				/>
 			</div>
 			<button
-				on:click={sendOTP}
+				onclick={sendOTP}
 				disabled={loading}
 				class="w-full bg-blue-500 text-white py-2 px-4 rounded-md hover:bg-blue-600 disabled:opacity-50"
 			>
@@ -151,14 +151,14 @@
 			</div>
 			<div class="flex space-x-2">
 				<button
-					on:click={verifyOTP}
+					onclick={verifyOTP}
 					disabled={loading}
 					class="flex-1 bg-green-500 text-white py-2 px-4 rounded-md hover:bg-green-600 disabled:opacity-50"
 				>
 					{loading ? 'Verifying...' : 'Verify OTP'}
 				</button>
 				<button
-					on:click={() => step = 'email'}
+					onclick={() => step = 'email'}
 					class="px-4 py-2 border rounded-md hover:bg-gray-50"
 				>
 					Back
@@ -172,7 +172,7 @@
 			<h3 class="text-lg font-semibold text-green-700">Success!</h3>
 			<p class="text-gray-600">OTP verification completed successfully.</p>
 			<button
-				on:click={reset}
+				onclick={reset}
 				class="bg-blue-500 text-white py-2 px-4 rounded-md hover:bg-blue-600"
 			>
 				Try Again
diff --git a/src/routes/unauthorized/+page.svelte b/src/routes/unauthorized/+page.svelte
new file mode 100644
index 0000000..ab576e2
--- /dev/null
+++ b/src/routes/unauthorized/+page.svelte
@@ -0,0 +1,46 @@
+<script lang="ts">
+	import { signOut } from '$lib/authClient';
+	import { goto } from '$app/navigation';
+
+	const handleSignOut = async () => {
+		await signOut({
+			fetchOptions: {
+				onSuccess: () => {
+					goto('/', { invalidateAll: true });
+				}
+			}
+		});
+	};
+</script>
+
+<div class="mx-auto max-w-md p-8 text-center">
+	<div class="mb-6">
+		<div class="mx-auto mb-4 h-16 w-16 rounded-full bg-red-100 flex items-center justify-center">
+			<svg class="h-8 w-8 text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
+				<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z" />
+			</svg>
+		</div>
+		<h1 class="text-2xl font-bold text-gray-900">Access Restricted</h1>
+	</div>
+	
+	<p class="mb-6 text-gray-600">
+		This area is restricted to authorized administrators only. 
+		If you believe you should have access, please contact the site administrator.
+	</p>
+	
+	<div class="space-y-3">
+		<button
+			onclick={handleSignOut}
+			class="w-full rounded-md bg-gray-600 px-4 py-2 text-white hover:bg-gray-700"
+		>
+			Sign Out
+		</button>
+		
+		<a
+			href="/"
+			class="block w-full rounded-md border border-gray-300 px-4 py-2 text-gray-700 hover:bg-gray-50"
+		>
+			Return to Home
+		</a>
+	</div>
+</div>
