diff --git a/src/hooks.server.ts b/src/hooks.server.ts
index 60d814c..4f08130 100644
--- a/src/hooks.server.ts
+++ b/src/hooks.server.ts
@@ -2,6 +2,7 @@ import { auth } from '$lib/auth';
 import { sequence } from '@sveltejs/kit/hooks';
 import { svelteKitHandler } from 'better-auth/svelte-kit';
 import { redirect, type Handle } from '@sveltejs/kit';
+import { canAccessAdminRoutes } from '$lib/server/adminConfig';
 
 const betterAuthHandler: Handle = async ({ event, resolve }) => {
 	return svelteKitHandler({ event, resolve, auth });
@@ -17,8 +18,14 @@ const authorizationHandler: Handle = async ({ event, resolve }) => {
 			headers: event.request.headers 
 		});
 		
-		if (routeId.startsWith('/app') && !session) {
-			redirect(303, '/signin');
+		if (routeId.startsWith('/app')) {
+			if (!session) {
+				redirect(303, '/signin');
+			}
+			// Check if user has admin role for /app routes
+			if (!canAccessAdminRoutes(session.user.role)) {
+				redirect(303, '/unauthorized');
+			}
 		}
 		
 		if (routeId.startsWith('/(auth)') && session) {
diff --git a/src/lib/auth.ts b/src/lib/auth.ts
index 82ef43c..a3604bd 100644
--- a/src/lib/auth.ts
+++ b/src/lib/auth.ts
@@ -1,9 +1,11 @@
 import { betterAuth } from 'better-auth';
 import { drizzleAdapter } from 'better-auth/adapters/drizzle';
 import { emailOTP } from 'better-auth/plugins';
+import { createAuthMiddleware, APIError } from 'better-auth/api';
 import { db } from './server/db';
 import * as schema from './server/db/schema';
 import { BETTER_AUTH_SECRET, BETTER_AUTH_URL } from '$env/static/private';
+import { isAdminEmail, ADMIN_CONFIG } from './server/adminConfig';
 
 export const auth = betterAuth({
 	database: drizzleAdapter(db, { 
@@ -18,11 +20,34 @@ export const auth = betterAuth({
 			generateId: false
 		}
 	},
-	emailAndPassword: { 
-		enabled: true, 
+	emailAndPassword: {
+		enabled: true,
 		autoSignIn: true,
 		requireEmailVerification: true
 	},
+	hooks: {
+		before: createAuthMiddleware(async (ctx) => {
+			// Restrict registration to admin emails only
+			if (ctx.path === '/sign-up/email') {
+				const email = ctx.body?.email as string;
+				if (!email || !isAdminEmail(email)) {
+					throw new APIError('BAD_REQUEST', {
+						message: 'Registration is restricted to authorized administrators only.'
+					});
+				}
+				// Add admin role to the request body
+				return {
+					context: {
+						...ctx,
+						body: {
+							...ctx.body,
+							role: ADMIN_CONFIG.defaultRole
+						}
+					}
+				};
+			}
+		})
+	},
 	rateLimit: { storage: 'database' },
 	plugins: [
 		emailOTP({
diff --git a/src/lib/server/adminConfig.ts b/src/lib/server/adminConfig.ts
new file mode 100644
index 0000000..f73def0
--- /dev/null
+++ b/src/lib/server/adminConfig.ts
@@ -0,0 +1,43 @@
+// Admin configuration - centralized admin management
+// This keeps admin logic separate and easily extensible
+
+export const ADMIN_CONFIG = {
+	// Allowed admin emails - only these can register/sign in to /app routes
+	allowedAdminEmails: [
+		// TODO: Replace with your actual admin email
+		'<EMAIL>', // ⚠️ CHANGE THIS to your real email address
+		// '<EMAIL>', // Add more as needed
+	],
+	
+	// Default role for new registrations (will be 'admin' for allowed emails)
+	defaultRole: 'admin' as const,
+	
+	// Future: role-based permissions (when you add more user types)
+	permissions: {
+		admin: {
+			canManageWebsites: true,
+			canPublishWebsites: true,
+			canDeleteWebsites: true,
+			canManageUsers: true, // For future use
+		},
+		// Future roles:
+		// contributor: { canManageWebsites: true, canPublishWebsites: false },
+		// expert: { canManageWebsites: true, canPublishWebsites: true },
+		// advertiser: { canManageWebsites: false, canPublishWebsites: false },
+	}
+} as const;
+
+// Helper functions
+export function isAdminEmail(email: string): boolean {
+	return ADMIN_CONFIG.allowedAdminEmails.includes(email.toLowerCase());
+}
+
+export function canAccessAdminRoutes(userRole: string): boolean {
+	// For now, only admins can access /app routes
+	// Later: extend this for other roles as needed
+	return userRole === 'admin';
+}
+
+// Type definitions for future use
+export type UserRole = 'admin' | 'contributor' | 'expert' | 'advertiser';
+export type Permission = keyof typeof ADMIN_CONFIG.permissions.admin;
diff --git a/src/lib/server/db/schema.ts b/src/lib/server/db/schema.ts
index d112f08..011eb95 100644
--- a/src/lib/server/db/schema.ts
+++ b/src/lib/server/db/schema.ts
@@ -9,6 +9,8 @@ export const users = pgTable('users', {
 	email: text('email').notNull().unique(),
 	emailVerified: boolean('email_verified').notNull(),
 	image: text('image'),
+	// User role for future extensibility
+	role: text('role').default('admin').notNull(), // 'admin' | 'contributor' | 'expert' | 'advertiser'
 	createdAt: timestamp('created_at').notNull(),
 	updatedAt: timestamp('updated_at').notNull()
 });
diff --git a/src/routes/(auth)/signup/+page.svelte b/src/routes/(auth)/signup/+page.svelte
index 64b9d30..80f9e60 100644
--- a/src/routes/(auth)/signup/+page.svelte
+++ b/src/routes/(auth)/signup/+page.svelte
@@ -28,7 +28,12 @@
 				});
 				
 				if (result.error) {
-					error = result.error.message ?? 'An unknown error occurred';
+					// Handle admin-only registration error
+					if (result.error.message?.includes('restricted to authorized administrators')) {
+						error = 'Registration is currently restricted to authorized administrators only. Please contact the site administrator if you need access.';
+					} else {
+						error = result.error.message ?? 'An unknown error occurred';
+					}
 				} else {
 					// Account created, now send verification OTP
 					const otpResult = await authClient.emailOtp.sendVerificationOtp({
@@ -106,6 +111,10 @@
 	<!-- Sign Up Form -->
 	<form class="mx-auto mt-10 flex max-w-md flex-col gap-4" onsubmit={handleSubmit} novalidate>
 		<h2 class="mb-4 text-2xl font-bold text-center">Create Account</h2>
+
+		<div class="mb-4 rounded-md bg-amber-50 border border-amber-200 p-3 text-sm text-amber-800">
+			<strong>Admin Access Only:</strong> Registration is currently restricted to authorized administrators.
+		</div>
 		
 		<!-- Hidden username field for accessibility -->
 		<input
diff --git a/src/routes/admin-test/+page.svelte b/src/routes/admin-test/+page.svelte
new file mode 100644
index 0000000..dcf6532
--- /dev/null
+++ b/src/routes/admin-test/+page.svelte
@@ -0,0 +1,96 @@
+<script lang="ts">
+	import { useSession } from '$lib/authClient';
+	
+	const session = useSession();
+</script>
+
+<div class="mx-auto max-w-2xl p-8">
+	<h1 class="mb-6 text-3xl font-bold">Admin System Test</h1>
+	
+	{#if $session}
+		<div class="mb-6 rounded-lg bg-green-50 border border-green-200 p-4">
+			<h2 class="mb-2 text-lg font-semibold text-green-800">✅ Authentication Working</h2>
+			<div class="text-sm text-green-700">
+				<p><strong>User ID:</strong> {$session.user.id}</p>
+				<p><strong>Email:</strong> {$session.user.email}</p>
+				<p><strong>Name:</strong> {$session.user.name}</p>
+				<p><strong>Role:</strong> {$session.user.role}</p>
+				<p><strong>Email Verified:</strong> {$session.user.emailVerified ? 'Yes' : 'No'}</p>
+			</div>
+		</div>
+		
+		{#if $session.user.role === 'admin'}
+			<div class="mb-6 rounded-lg bg-blue-50 border border-blue-200 p-4">
+				<h2 class="mb-2 text-lg font-semibold text-blue-800">🔑 Admin Access Confirmed</h2>
+				<p class="text-sm text-blue-700">
+					You have admin privileges and can access all CMS functionality.
+				</p>
+			</div>
+			
+			<div class="space-y-3">
+				<a 
+					href="/app" 
+					class="block rounded-md bg-blue-600 px-4 py-2 text-center text-white hover:bg-blue-700"
+				>
+					Go to Admin Dashboard
+				</a>
+				<a 
+					href="/app/add" 
+					class="block rounded-md border border-blue-600 px-4 py-2 text-center text-blue-600 hover:bg-blue-50"
+				>
+					Add Website
+				</a>
+				<a 
+					href="/app/manage" 
+					class="block rounded-md border border-blue-600 px-4 py-2 text-center text-blue-600 hover:bg-blue-50"
+				>
+					Manage Websites
+				</a>
+			</div>
+		{:else}
+			<div class="mb-6 rounded-lg bg-red-50 border border-red-200 p-4">
+				<h2 class="mb-2 text-lg font-semibold text-red-800">❌ No Admin Access</h2>
+				<p class="text-sm text-red-700">
+					Your account role is "{$session.user.role}" but admin access requires "admin" role.
+				</p>
+			</div>
+		{/if}
+	{:else}
+		<div class="mb-6 rounded-lg bg-yellow-50 border border-yellow-200 p-4">
+			<h2 class="mb-2 text-lg font-semibold text-yellow-800">⚠️ Not Authenticated</h2>
+			<p class="text-sm text-yellow-700 mb-3">
+				You need to sign in to test the admin system.
+			</p>
+			<div class="space-y-2">
+				<a 
+					href="/signin" 
+					class="block rounded-md bg-yellow-600 px-4 py-2 text-center text-white hover:bg-yellow-700"
+				>
+					Sign In
+				</a>
+				<a 
+					href="/signup" 
+					class="block rounded-md border border-yellow-600 px-4 py-2 text-center text-yellow-600 hover:bg-yellow-50"
+				>
+					Create Admin Account
+				</a>
+			</div>
+		</div>
+	{/if}
+	
+	<div class="mt-8 rounded-lg bg-gray-50 border border-gray-200 p-4">
+		<h2 class="mb-2 text-lg font-semibold text-gray-800">📋 Setup Checklist</h2>
+		<ul class="text-sm text-gray-700 space-y-1">
+			<li>✅ Database migration completed (role column added)</li>
+			<li>⚠️ Update your email in <code>src/lib/server/adminConfig.ts</code></li>
+			<li>🔄 Restart dev server after email change</li>
+			<li>📧 Test signup with your admin email</li>
+			<li>🔐 Verify OTP code (check console)</li>
+			<li>🎯 Access admin dashboard at <code>/app</code></li>
+		</ul>
+	</div>
+	
+	<div class="mt-6 text-center">
+		<a href="/" class="text-blue-600 hover:underline">← Back to Home</a>
+	</div>
+</div>
diff --git a/src/routes/unauthorized/+page.svelte b/src/routes/unauthorized/+page.svelte
new file mode 100644
index 0000000..ab576e2
--- /dev/null
+++ b/src/routes/unauthorized/+page.svelte
@@ -0,0 +1,46 @@
+<script lang="ts">
+	import { signOut } from '$lib/authClient';
+	import { goto } from '$app/navigation';
+
+	const handleSignOut = async () => {
+		await signOut({
+			fetchOptions: {
+				onSuccess: () => {
+					goto('/', { invalidateAll: true });
+				}
+			}
+		});
+	};
+</script>
+
+<div class="mx-auto max-w-md p-8 text-center">
+	<div class="mb-6">
+		<div class="mx-auto mb-4 h-16 w-16 rounded-full bg-red-100 flex items-center justify-center">
+			<svg class="h-8 w-8 text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
+				<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z" />
+			</svg>
+		</div>
+		<h1 class="text-2xl font-bold text-gray-900">Access Restricted</h1>
+	</div>
+	
+	<p class="mb-6 text-gray-600">
+		This area is restricted to authorized administrators only. 
+		If you believe you should have access, please contact the site administrator.
+	</p>
+	
+	<div class="space-y-3">
+		<button
+			onclick={handleSignOut}
+			class="w-full rounded-md bg-gray-600 px-4 py-2 text-white hover:bg-gray-700"
+		>
+			Sign Out
+		</button>
+		
+		<a
+			href="/"
+			class="block w-full rounded-md border border-gray-300 px-4 py-2 text-gray-700 hover:bg-gray-50"
+		>
+			Return to Home
+		</a>
+	</div>
+</div>
