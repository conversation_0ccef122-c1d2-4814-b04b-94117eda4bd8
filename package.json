{"name": "boilerplate", "private": true, "version": "0.0.1", "type": "module", "scripts": {"dev": "vite dev", "build": "vite build", "preview": "vite preview", "start": "bun ./build/index.js", "prepare": "svelte-kit sync || echo ''", "check": "svelte-kit sync && svelte-check --tsconfig ./tsconfig.json", "check:watch": "svelte-kit sync && svelte-check --tsconfig ./tsconfig.json --watch", "format": "prettier --write .", "lint": "prettier --check .", "db:start": "docker compose up -d", "db:push": "drizzle-kit push", "db:generate": "drizzle-kit generate", "db:migrate": "drizzle-kit migrate", "db:studio": "drizzle-kit studio", "db:check": "drizzle-kit check", "changelog": "node scripts/changelog.js"}, "devDependencies": {"@sveltejs/adapter-auto": "^4.0.0", "@sveltejs/kit": "^2.19.2", "@sveltejs/vite-plugin-svelte": "^5.0.3", "@tailwindcss/typography": "^0.5.16", "@tailwindcss/vite": "^4.0.14", "@types/bun": "^1.2.5", "@types/pg": "^8.11.11", "drizzle-kit": "^0.30.5", "prettier": "^3.5.3", "prettier-plugin-svelte": "^3.3.3", "prettier-plugin-tailwindcss": "^0.6.11", "svelte": "^5.23.0", "svelte-check": "^4.1.5", "tailwindcss": "^4.0.14", "typescript": "^5.8.2", "vite": "^6.2.2"}, "dependencies": {"better-auth": "^1.2.8", "bits-ui": "^1.3.12", "drizzle-orm": "^0.40.0", "lucide-svelte": "^0.482.0", "postgres": "^3.4.5"}}