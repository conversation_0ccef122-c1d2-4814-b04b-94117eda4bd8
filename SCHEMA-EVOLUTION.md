# Schema Evolution Guide

## Quick Start - Your Iterative Workflow

### 1. Start Minimal
Begin with the simplest possible schema that solves your immediate need.

### 2. Add Fields Incrementally
When you need a new field:

```bash
# 1. Edit schema.ts - add your field
# 2. Generate migration
bun db:generate

# 3. Apply to database
bun db:migrate
```

### 3. Common Evolution Patterns

#### Adding a nullable field (safest):
```typescript
// In schema.ts
description: text('description'), // nullable by default
```

#### Adding with default value:
```typescript
status: text('status').default('active').notNull(),
priority: integer('priority').default(0).notNull(),
```

#### Adding JSON for flexible data:
```typescript
metadata: jsonb('metadata').default({}),
```

### 4. Data Safety Tips

1. **Always add nullable fields or defaults** - prevents breaking existing data
2. **Test migrations locally first** - use `bun db:push` for rapid prototyping
3. **Keep migrations small** - one logical change at a time
4. **Use Drizzle Studio** - `bun db:studio` to inspect your data

### 5. Example Evolution Path

```typescript
// Day 1: Just a name
export const items = pgTable('items', {
  id: uuid('id').primaryKey().defaultRandom(),
  userId: uuid('user_id').notNull().references(() => users.id),
  name: text('name').notNull(),
  createdAt: timestamp('created_at').notNull().defaultNow()
});

// Week 2: Users want descriptions
// Add: description: text('description'),

// Week 4: Need status tracking
// Add: status: text('status').default('active'),

// Month 2: Categories emerge
// Create new table: categories
// Add: categoryId: uuid('category_id').references(() => categories.id)

// Month 3: Need flexible attributes
// Add: metadata: jsonb('metadata').default({})
```

### 6. Commands Reference

- `bun db:generate` - Create migration from schema changes
- `bun db:migrate` - Apply migrations to database
- `bun db:push` - Push schema directly (dev only!)
- `bun db:studio` - Visual database browser
- `bun db:check` - Verify schema consistency

### 7. Rollback Strategy

If a migration goes wrong:

1. **Immediate rollback**: Delete the migration file and regenerate
2. **After deployment**: Create a new migration that reverses the change
3. **Data preservation**: Always backup before major changes

### 8. Production Considerations

- Run migrations during low-traffic periods
- Use database transactions for complex migrations
- Consider feature flags for gradual rollouts
- Monitor performance after schema changes

## Remember: Start simple, iterate based on real needs! 