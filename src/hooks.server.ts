import { auth } from '$lib/auth';
import { sequence } from '@sveltejs/kit/hooks';
import { svelteKitHandler } from 'better-auth/svelte-kit';
import { redirect, type Handle } from '@sveltejs/kit';
import { canAccessAdminRoutes } from '$lib/server/adminConfig';

const betterAuthHandler: Handle = async ({ event, resolve }) => {
	return svelteKitHandler({ event, resolve, auth });
};

const authorizationHandler: Handle = async ({ event, resolve }) => {
	const routeId = event.route.id;
	if (!routeId) return resolve(event);
	
	// Only check session for protected routes to avoid unnecessary DB calls
	if (routeId.startsWith('/app') || routeId.startsWith('/(auth)')) {
		const session = await auth.api.getSession({
			headers: event.request.headers
		});

		// Store session in locals for use in loaders/actions (avoids duplicate DB calls)
		event.locals.auth = session as any;

		if (routeId.startsWith('/app')) {
			if (!session) {
				throw redirect(303, '/signin');
			}
			// Check if user has admin role for /app routes
			if (!canAccessAdminRoutes((session.user as any).role)) {
				throw redirect(303, '/unauthorized');
			}
		}

		if (routeId.startsWith('/(auth)') && session) {
			throw redirect(303, '/app');
		}
	}
	
	return resolve(event);
};

export const handle = sequence(betterAuthHandler, authorizationHandler);