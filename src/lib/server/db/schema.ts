import { pgTable, uuid, text, timestamp, boolean } from 'drizzle-orm/pg-core';
import { baseFields, userOwned } from './schemaHelpers';

// ====== AUTHENTICATION TABLES (Better Auth) ======

export const users = pgTable('users', {
	id: uuid('id').primaryKey().defaultRandom(),
	name: text('name').notNull(),
	email: text('email').notNull().unique(),
	emailVerified: boolean('email_verified').notNull(),
	image: text('image'),
	// User role for future extensibility
	role: text('role').default('admin').notNull(), // 'admin' | 'contributor' | 'expert' | 'advertiser'
	createdAt: timestamp('created_at').notNull(),
	updatedAt: timestamp('updated_at').notNull()
});

export const sessions = pgTable('sessions', {
	id: uuid('id').primaryKey().defaultRandom(),
	expiresAt: timestamp('expires_at').notNull(),
	token: text('token').notNull().unique(),
	createdAt: timestamp('created_at').notNull(),
	updatedAt: timestamp('updated_at').notNull(),
	ipAddress: text('ip_address'),
	userAgent: text('user_agent'),
	userId: uuid('user_id')
		.notNull()
		.references(() => users.id, { onDelete: 'cascade' })
});

export const accounts = pgTable('accounts', {
	id: uuid('id').primaryKey().defaultRandom(),
	accountId: text('account_id').notNull(),
	providerId: text('provider_id').notNull(),
	userId: uuid('user_id')
		.notNull()
		.references(() => users.id, { onDelete: 'cascade' }),
	accessToken: text('access_token'),
	refreshToken: text('refresh_token'),
	idToken: text('id_token'),
	accessTokenExpiresAt: timestamp('access_token_expires_at'),
	refreshTokenExpiresAt: timestamp('refresh_token_expires_at'),
	scope: text('scope'),
	password: text('password'),
	createdAt: timestamp('created_at').notNull(),
	updatedAt: timestamp('updated_at').notNull()
});

export const verifications = pgTable('verifications', {
	id: uuid('id').primaryKey().defaultRandom(),
	identifier: text('identifier').notNull(),
	value: text('value').notNull(),
	expiresAt: timestamp('expires_at').notNull(),
	createdAt: timestamp('created_at'),
	updatedAt: timestamp('updated_at')
});

// ====== TAILWIND CSS WEBSITES GALLERY TABLES ======

// Core website showcase table - START SIMPLE!
export const websites = pgTable('websites', {
	...baseFields,    // Adds: id, createdAt, updatedAt
	...userOwned,     // Adds: userId foreign key (admin who added the website)
	
	// Core website showcase fields
	title: text('title').notNull(), // Website name/title
	tailwindVersion: text('tailwind_version').notNull(), // e.g., "3.4", "4.0"
	thumbnail: text('thumbnail').notNull(), // Preview image URL
	
	// Gallery management fields
	status: text('status').default('draft').notNull(), // 'draft' | 'published'
	publishedAt: timestamp('published_at') // null when draft
});

// Future iterations for WEBSITES GALLERY (DO NOT ADD YET - just ideas for later):
// - url: text('url').notNull() // Live website URL
// - description: text('description') // Website description
// - uiLibraries: text('ui_libraries').array() // ['shadcn', 'headlessui', 'daisyui']
// - categories: relation to categories table (e.g., 'ecommerce', 'portfolio', 'landing')
// - views: integer('views').default(0) // Track popularity
// - likes: integer('likes').default(0) // User engagement
// - difficulty: text('difficulty') // 'beginner', 'intermediate', 'advanced'
// - frameworks: text('frameworks').array() // ['nextjs', 'svelte', 'vue', 'react']
// - features: text('features').array() // ['responsive', 'dark-mode', 'animations']
// - sourceCodeUrl: text('source_code_url') // GitHub/CodePen link
// - demoUrl: text('demo_url') // Live demo link
// - submittedBy: uuid('submitted_by') // Community submissions
// - metadata: jsonb('metadata') // Flexible field for experiments
// etc.
