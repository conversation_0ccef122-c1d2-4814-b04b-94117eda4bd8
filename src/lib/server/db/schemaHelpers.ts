import { timestamp, uuid, text, jsonb } from 'drizzle-orm/pg-core';
import { users } from './schema';

// Common field patterns to copy-paste when evolving schema

export const baseFields = {
	id: uuid('id').primaryKey().defaultRandom(),
	createdAt: timestamp('created_at').notNull().defaultNow(),
	updatedAt: timestamp('updated_at').notNull().defaultNow()
};

export const userOwned = {
	userId: uuid('user_id')
		.notNull()
		.references(() => users.id, { onDelete: 'cascade' })
};

export const softDelete = {
	deletedAt: timestamp('deleted_at'),
	isDeleted: text('is_deleted').default('false')
};

export const versionable = {
	version: text('version').default('1'),
	previousVersionId: uuid('previous_version_id')
};

export const flexible = {
	metadata: jsonb('metadata').default({}),
	tags: text('tags').array()
};

// Example usage in schema.ts:
// export const myTable = pgTable('my_table', {
//   ...baseFields,
//   ...userOwned,
//   name: text('name').notNull(),
//   ...flexible  // Add this when you need flexibility
// }); 