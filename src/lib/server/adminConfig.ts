// Admin configuration - centralized admin management
// This keeps admin logic separate and easily extensible

import { ADMIN_EMAILS } from '$env/static/private';

// Parse admin emails from environment variable (O(1) lookup with Set)
const ADMIN_EMAIL_SET = new Set(
	ADMIN_EMAILS?.split(',').map((email: string) => email.trim().toLowerCase()) ?? []
);

// Startup-time guardrail: warn if no admin emails configured
if (ADMIN_EMAIL_SET.size === 0) {
	console.warn('[admin] ADMIN_EMAILS empty – no one can sign up');
}

export const ADMIN_CONFIG = {
	// Default role for new registrations (will be 'admin' for allowed emails)
	defaultRole: 'admin' as const,
} as const;

// Consistent error message for unauthorized access
export const ADMIN_ONLY_ERROR = 'Registration is restricted to authorized administrators only.';

// Helper functions
export function isAdminEmail(email: string): boolean {
	return ADMIN_EMAIL_SET.has(email.trim().toLowerCase());
}

export function canAccessAdminRoutes(userRole: string): boolean {
	// For now, only admins can access /app routes
	// Later: extend this for other roles as needed
	return userRole === 'admin';
}

// Type definitions for future use
export type UserRole = 'admin' | 'contributor' | 'expert' | 'advertiser';
