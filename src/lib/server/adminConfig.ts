// Admin configuration - centralized admin management
// This keeps admin logic separate and easily extensible

export const ADMIN_CONFIG = {
	// Allowed admin emails - only these can register/sign in to /app routes
	allowedAdminEmails: [
		// TODO: Replace with your actual admin email
		'<EMAIL>', // ⚠️ CHANGE THIS to your real email address
		// '<EMAIL>', // Add more as needed
	],
	
	// Default role for new registrations (will be 'admin' for allowed emails)
	defaultRole: 'admin' as const,
	
	// Future: role-based permissions (when you add more user types)
	permissions: {
		admin: {
			canManageWebsites: true,
			canPublishWebsites: true,
			canDeleteWebsites: true,
			canManageUsers: true, // For future use
		},
		// Future roles:
		// contributor: { canManageWebsites: true, canPublishWebsites: false },
		// expert: { canManageWebsites: true, canPublishWebsites: true },
		// advertiser: { canManageWebsites: false, canPublishWebsites: false },
	}
} as const;

// Helper functions
export function isAdminEmail(email: string): boolean {
	return ADMIN_CONFIG.allowedAdminEmails.some(allowedEmail => allowedEmail.toLowerCase() === email.toLowerCase());
}

export function canAccessAdminRoutes(userRole: string): boolean {
	// For now, only admins can access /app routes
	// Later: extend this for other roles as needed
	return userRole === 'admin';
}

// Type definitions for future use
export type UserRole = 'admin' | 'contributor' | 'expert' | 'advertiser';
export type Permission = keyof typeof ADMIN_CONFIG.permissions.admin;
