import { betterAuth } from 'better-auth';
import { drizzleAdapter } from 'better-auth/adapters/drizzle';
import { emailOTP } from 'better-auth/plugins';
import { createAuthMiddleware, APIError } from 'better-auth/api';
import { db } from './server/db';
import * as schema from './server/db/schema';
import { BETTER_AUTH_SECRET, BETTER_AUTH_URL } from '$env/static/private';
import { isAdminEmail, ADMIN_CONFIG, ADMIN_ONLY_ERROR } from './server/adminConfig';

export const auth = betterAuth({
	database: drizzleAdapter(db, { 
		provider: 'pg',
		schema,
		usePlural: true
	}),
	secret: BETTER_AUTH_SECRET,
	baseURL: BETTER_AUTH_URL,
	advanced: {
		database: {
			generateId: false
		}
	},
	emailAndPassword: {
		enabled: true,
		autoSignIn: true,
		requireEmailVerification: true
	},

	hooks: {
		before: createAuthMiddleware(async (ctx) => {
			// Restrict registration to admin emails only
			if (ctx.path === '/sign-up/email') {
				const email = ctx.body?.email as string;
				if (!email || !isAdminEmail(email)) {
					throw new APIError('BAD_REQUEST', {
						message: ADMIN_ONLY_ERROR
					});
				}
				// Add admin role to the request body
				return {
					body: {
						...ctx.body,
						role: ADMIN_CONFIG.defaultRole
					}
				};
			}
		})
	},
	rateLimit: { storage: 'database' },
	plugins: [
		emailOTP({
			async sendVerificationOTP({ email, otp, type }) {
				// Console log for now as requested
				console.log(`\n=== EMAIL OTP VERIFICATION ===`);
				console.log(`Email: ${email}`);
				console.log(`OTP Code: ${otp}`);
				console.log(`Type: ${type}`);
				console.log(`===============================\n`);
				
				// TODO: Replace with actual email service (Resend, SendGrid, etc.)
				if (type === 'sign-in') {
					console.log('📧 Sending OTP for sign-in');
				} else if (type === 'email-verification') {
					console.log('✅ Sending OTP for email verification');
				} else if (type === 'forget-password') {
					console.log('🔑 Sending OTP for password reset');
				}
			},
			otpLength: 6,
			expiresIn: 300, // 5 minutes
			allowedAttempts: 3,
			sendVerificationOnSignUp: true
		})
	]
});
