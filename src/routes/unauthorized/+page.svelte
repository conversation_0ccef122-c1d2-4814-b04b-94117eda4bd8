<script lang="ts">
	import { signOut } from '$lib/authClient';
	import { goto } from '$app/navigation';

	const handleSignOut = async () => {
		await signOut({
			fetchOptions: {
				onSuccess: () => {
					goto('/', { invalidateAll: true });
				}
			}
		});
	};
</script>

<div class="mx-auto max-w-md p-8 text-center">
	<div class="mb-6">
		<div class="mx-auto mb-4 h-16 w-16 rounded-full bg-red-100 flex items-center justify-center">
			<svg class="h-8 w-8 text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
				<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z" />
			</svg>
		</div>
		<h1 class="text-2xl font-bold text-gray-900">Access Restricted</h1>
	</div>
	
	<p class="mb-6 text-gray-600">
		This area is restricted to authorized administrators only. 
		If you believe you should have access, please contact the site administrator.
	</p>
	
	<div class="space-y-3">
		<button
			onclick={handleSignOut}
			class="w-full rounded-md bg-gray-600 px-4 py-2 text-white hover:bg-gray-700"
		>
			Sign Out
		</button>
		
		<a
			href="/"
			class="block w-full rounded-md border border-gray-300 px-4 py-2 text-gray-700 hover:bg-gray-50"
		>
			Return to Home
		</a>
	</div>
</div>
