import { db } from '$lib/server/db';
import { websites } from '$lib/server/db/schema';
import { eq } from 'drizzle-orm';
import type { PageServerLoad } from './$types';

export const load = (async () => {
	// Fetch only published websites
	const publishedWebsites = await db
		.select()
		.from(websites)
		.where(eq(websites.status, 'published'))
		.orderBy(websites.publishedAt);

	return {
		websites: publishedWebsites
	};
}) satisfies PageServerLoad; 