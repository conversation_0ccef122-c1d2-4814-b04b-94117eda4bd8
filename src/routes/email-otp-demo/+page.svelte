<script lang="ts">
	import { authClient } from '$lib/authClient';
	import { goto } from '$app/navigation';

	let email = '';
	let otp = '';
	let loading = false;
	let message = '';
	let step: 'email' | 'otp' | 'success' = 'email';
	let otpType: 'sign-in' | 'email-verification' | 'forget-password' = 'email-verification';

	// Send OTP to email
	async function sendOTP() {
		if (!email) {
			message = 'Please enter your email';
			return;
		}

		loading = true;
		message = '';

		try {
			const { data, error } = await authClient.emailOtp.sendVerificationOtp({
				email,
				type: otpType
			});

			if (error) {
				message = `Error: ${error.message}`;
			} else {
				message = `OTP sent to ${email}! Check your console for the code.`;
				step = 'otp';
			}
		} catch (err) {
			message = `Failed to send OTP: ${err}`;
		} finally {
			loading = false;
		}
	}

	// Verify OTP and handle different types
	async function verifyOTP() {
		if (!otp) {
			message = 'Please enter the OTP code';
			return;
		}

		loading = true;
		message = '';

		try {
			let result;

			if (otpType === 'sign-in') {
				// Sign in with <PERSON><PERSON> (creates user if doesn't exist)
				result = await authClient.signIn.emailOtp({
					email,
					otp
				});
			} else if (otpType === 'email-verification') {
				// Verify email address
				result = await authClient.emailOtp.verifyEmail({
					email,
					otp
				});
			} else if (otpType === 'forget-password') {
				// Reset password (you'd typically collect new password here)
				result = await authClient.emailOtp.resetPassword({
					email,
					otp,
					password: 'newPassword123' // In real app, get from form
				});
			}

			if (result?.error) {
				message = `Error: ${result.error.message}`;
			} else {
				message = `Success! OTP verified for ${otpType}`;
				step = 'success';
				
				// If sign-in was successful, redirect to app
				if (otpType === 'sign-in' && result?.data) {
					setTimeout(() => goto('/app'), 2000);
				}
			}
		} catch (err) {
			message = `Failed to verify OTP: ${err}`;
		} finally {
			loading = false;
		}
	}

	function reset() {
		email = '';
		otp = '';
		message = '';
		step = 'email';
		loading = false;
	}
</script>

<div class="max-w-md mx-auto mt-8 p-6 bg-white rounded-lg shadow-md">
	<h2 class="text-2xl font-bold mb-6 text-center">Email OTP Demo</h2>

	<!-- OTP Type Selection -->
	<div class="mb-6">
		<label for="otp-type" class="block text-sm font-medium mb-2">OTP Type:</label>
		<select id="otp-type" bind:value={otpType} class="w-full p-2 border rounded-md" disabled={step !== 'email'}>
			<option value="email-verification">Email Verification</option>
			<option value="sign-in">Sign In (Passwordless)</option>
			<option value="forget-password">Password Reset</option>
		</select>
	</div>

	{#if step === 'email'}
		<!-- Email Input Step -->
		<div class="space-y-4">
			<div>
				<label for="email" class="block text-sm font-medium mb-1">Email Address</label>
				<input
					id="email"
					type="email"
					bind:value={email}
					placeholder="Enter your email"
					class="w-full p-2 border rounded-md focus:ring-2 focus:ring-blue-500"
					disabled={loading}
				/>
			</div>
			<button
				on:click={sendOTP}
				disabled={loading}
				class="w-full bg-blue-500 text-white py-2 px-4 rounded-md hover:bg-blue-600 disabled:opacity-50"
			>
				{loading ? 'Sending...' : 'Send OTP'}
			</button>
		</div>
	{:else if step === 'otp'}
		<!-- OTP Input Step -->
		<div class="space-y-4">
			<div>
				<label for="otp" class="block text-sm font-medium mb-1">Enter OTP Code</label>
				<input
					id="otp"
					type="text"
					bind:value={otp}
					placeholder="Enter 6-digit code"
					maxlength="6"
					class="w-full p-2 border rounded-md focus:ring-2 focus:ring-blue-500 text-center text-lg tracking-widest"
					disabled={loading}
				/>
			</div>
			<div class="flex space-x-2">
				<button
					on:click={verifyOTP}
					disabled={loading}
					class="flex-1 bg-green-500 text-white py-2 px-4 rounded-md hover:bg-green-600 disabled:opacity-50"
				>
					{loading ? 'Verifying...' : 'Verify OTP'}
				</button>
				<button
					on:click={() => step = 'email'}
					class="px-4 py-2 border rounded-md hover:bg-gray-50"
				>
					Back
				</button>
			</div>
		</div>
	{:else if step === 'success'}
		<!-- Success Step -->
		<div class="text-center space-y-4">
			<div class="text-green-500 text-4xl">✅</div>
			<h3 class="text-lg font-semibold text-green-700">Success!</h3>
			<p class="text-gray-600">OTP verification completed successfully.</p>
			<button
				on:click={reset}
				class="bg-blue-500 text-white py-2 px-4 rounded-md hover:bg-blue-600"
			>
				Try Again
			</button>
		</div>
	{/if}

	<!-- Message Display -->
	{#if message}
		<div class="mt-4 p-3 rounded-md {message.includes('Error') ? 'bg-red-100 text-red-700' : 'bg-blue-100 text-blue-700'}">
			{message}
		</div>
	{/if}

	<!-- Instructions -->
	<div class="mt-6 p-4 bg-gray-50 rounded-md">
		<h4 class="font-semibold mb-2">📋 How it works:</h4>
		<ol class="text-sm space-y-1 list-decimal list-inside">
			<li>Choose OTP type and enter email</li>
			<li>Check console for OTP code (since we're logging for now)</li>
			<li>Enter the 6-digit code to verify</li>
			<li>Success! User is verified/signed in based on type</li>
		</ol>
	</div>
</div>