<script lang="ts">
	import { signIn, authClient } from '$lib/authClient';
	import { goto } from '$app/navigation';

	let email = $state('');
	let password = $state('');
	let otpCode = $state('');
	let error = $state('');
	let loading = $state(false);
	let rateLimited = $state(false);
	let useOTP = $state(false);
	let otpSent = $state(false);
	let isEmailVerification = $state(false);
	let originalPassword = $state(''); // Store original password for post-verification sign-in

	async function handleSubmit(event: Event) {
		event.preventDefault();
		if (loading) return; // Prevent double-submission
		
		error = '';
		loading = true;
		rateLimited = false;

		try {
			if (useOTP) {
				if (!otpSent) {
					// Send OTP
					const result = await authClient.emailOtp.sendVerificationOtp({
						email,
						type: 'sign-in'
					});

					if (result.error) {
						if (result.error.status === 429) {
							rateLimited = true;
							error = 'Too many attempts. Please try again later.';
						} else {
							error = result.error.message ?? 'Failed to send OTP';
						}
					} else {
						otpSent = true;
						error = '';
					}
				} else {
					// Verify OTP - check if this is email verification or regular sign-in
					if (isEmailVerification) {
						// Email verification flow
						const verifyResult = await authClient.emailOtp.verifyEmail({
							email,
							otp: otpCode
						});

						if (verifyResult.error) {
							if (verifyResult.error.status === 429) {
								rateLimited = true;
								error = 'Too many attempts. Please try again later.';
							} else {
								error = verifyResult.error.message ?? 'Invalid verification code';
							}
						} else {
						// Email verification successful, now sign in with email/password using stored password
						const signInResult = await signIn.email({
							email,
							password: originalPassword,
							rememberMe: true,
							callbackURL: '/app'
						});

						if (signInResult.error) {
							error = 'Email verified but sign-in failed. Please try signing in again.';
						} else {
							goto(signInResult.data?.url ?? '/app');
						}
					}
					} else {
						// Regular OTP sign-in flow
						const result = await authClient.signIn.emailOtp({
							email,
							otp: otpCode
						});

						if (result.error) {
							if (result.error.status === 429) {
								rateLimited = true;
								error = 'Too many attempts. Please try again later.';
							} else {
								error = result.error.message ?? 'Invalid OTP code';
							}
						} else {
							// Successful sign in, redirect to app
							goto('/app');
						}
					}
				}
			} else {
				// Traditional email/password sign in
				const result = await signIn.email({
					email,
					password,
					rememberMe: true,
					callbackURL: '/app'
				});

				if (result.error) {
					// Check for rate limit error
					if (result.error.status === 429) {
						rateLimited = true;
						error = 'Too many attempts. Please try again later.';
					} else if (result.error.status === 403) {
					// Email verification required (403 status) - automatically switch to OTP mode and send verification
					useOTP = true;
					isEmailVerification = true;
					
					// Store original password before clearing it
					originalPassword = password;
					
					// Automatically send verification email
					const otpResult = await authClient.emailOtp.sendVerificationOtp({
						email,
						type: 'email-verification'
					});
					
					if (otpResult.error) {
						error = 'Failed to send verification email. Please try again.';
						useOTP = false;
						isEmailVerification = false;
						originalPassword = '';
					} else {
						otpSent = true;
						error = '';
						// Clear password since we're switching to OTP mode
						password = '';
					}
					} else {
						error = result.error.message ?? 'An unknown error occurred';
					}
				} else {
					goto(result.data?.url ?? '/app');
				}
			}
		} finally {
			loading = false;
		}
	}

	function toggleOTP() {
		useOTP = !useOTP;
		otpSent = false;
		otpCode = '';
		password = '';
		error = '';
		isEmailVerification = false;
		originalPassword = '';
	}

	function resetOTP() {
		otpSent = false;
		otpCode = '';
		error = '';
		isEmailVerification = false;
		originalPassword = '';
	}
</script>

<!-- Add rate limit warning -->
{#if rateLimited}
	<div class="mb-4 rounded bg-yellow-50 p-4 text-yellow-800">
		You've made too many login attempts. Please wait a few minutes before trying again.
	</div>
{/if}

<div class="mx-auto mt-10 max-w-md">
	<!-- Toggle between password and OTP -->
	<div class="mb-6 flex justify-center">
		<div class="flex rounded-lg bg-gray-100 p-1">
			<button
				type="button"
				class="rounded-md px-4 py-2 text-sm font-medium transition-colors {!useOTP ? 'bg-white text-gray-900 shadow-sm' : 'text-gray-500 hover:text-gray-700'}"
				onclick={() => !loading && toggleOTP()}
				disabled={loading}
			>
				Password
			</button>
			<button
				type="button"
				class="rounded-md px-4 py-2 text-sm font-medium transition-colors {useOTP ? 'bg-white text-gray-900 shadow-sm' : 'text-gray-500 hover:text-gray-700'}"
				onclick={() => !loading && toggleOTP()}
				disabled={loading}
			>
				OTP (Passwordless)
			</button>
		</div>
	</div>

	<form class="mx-auto mt-10 flex max-w-md flex-col gap-4" onsubmit={handleSubmit} novalidate>
		<!-- Hidden username field for accessibility when using password -->
		{#if !useOTP}
			<input
				type="email"
				name="username"
				value={email}
				autocomplete="username"
				style="display: none;"
				readonly
				tabindex="-1"
				aria-hidden="true"
			/>
		{/if}
		
		<input
			type="email"
			name="email"
			placeholder="Email"
			class="rounded-md border border-gray-300 p-2"
			bind:value={email}
			disabled={loading || otpSent}
			autocomplete="email"
			required
		/>

		{#if useOTP}
			{#if otpSent}
				<div class="rounded-md bg-green-50 p-3 text-sm text-green-800">
					{#if isEmailVerification}
						✅ Email verification required! We've sent a verification code to {email}. Please enter it below to verify your email and sign in.
					{:else}
						📧 OTP sent to {email}. Check your email and enter the code below.
					{/if}
				</div>
				<input
					type="text"
					name="otp"
					placeholder="Enter OTP code"
					class="rounded-md border border-gray-300 p-2 text-center font-mono text-lg tracking-widest"
					bind:value={otpCode}
					disabled={loading}
					required
					maxlength="6"
					autocomplete="one-time-code"
				/>
				<button
					type="button"
					class="text-sm text-blue-500 hover:text-blue-700"
					onclick={resetOTP}
					disabled={loading}
				>
					← Back to email
				</button>
			{/if}
		{:else}
			<input
				type="password"
				name="password"
				placeholder="Password"
				class="rounded-md border border-gray-300 p-2"
				bind:value={password}
				disabled={loading}
				autocomplete="current-password"
				required
			/>
		{/if}

		<button 
			type="submit" 
			class="rounded-md bg-blue-500 p-2 text-white disabled:opacity-50 disabled:cursor-not-allowed"
			disabled={loading || (useOTP && otpSent && !otpCode.trim())}
			aria-describedby={error ? 'error-message' : undefined}
		>
			{#if loading}
				{#if useOTP && !otpSent}
					{isEmailVerification ? 'Sending verification...' : 'Sending OTP...'}
				{:else if useOTP}
					{isEmailVerification ? 'Verifying email...' : 'Verifying...'}
				{:else}
					'Signing in...'
				{/if}
			{:else}
				{#if useOTP && !otpSent}
					{isEmailVerification ? 'Send Verification' : 'Send OTP'}
				{:else if useOTP}
					{isEmailVerification ? 'Verify Email & Sign in' : 'Verify & Sign in'}
				{:else}
					'Sign in'
				{/if}
			{/if}
		</button>

		<p class="mt-4 text-sm text-gray-500">
			Don't have an account? <a href="/signup" class="text-blue-500">Sign up</a>
		</p>

		{#if error}
			<div id="error-message" class="mt-4 rounded-md bg-red-50 border border-red-200 p-3 text-sm text-red-700" role="alert">
				{error}
			</div>
		{/if}
	</form>
</div>
