<script lang="ts">
	import { signUp, signIn, authClient } from '$lib/authClient';
	import { goto } from '$app/navigation';
	
	let email = $state('');
	let password = $state('');
	let otpCode = $state('');
	let error = $state('');
	let success = $state('');
	let loading = $state(false);
	let step = $state<'signup' | 'verify'>('signup');

	async function handleSubmit(event: Event) {
		event.preventDefault();
		if (loading) return; // Prevent double-submission
		
		error = '';
		success = '';
		loading = true;
		
		try {
			if (step === 'signup') {
				// Create account
				const result = await signUp.email({
					email,
					password,
					name: email.split('@')[0].replace(/[._-]/g, ' ').trim()
				});
				
				if (result.error) {
					error = result.error.message ?? 'An unknown error occurred';
				} else {
					// Account created, now send verification OTP
					const otpResult = await authClient.emailOtp.sendVerificationOtp({
						email,
						type: 'email-verification'
					});
					
					if (otpResult.error) {
						error = 'Account created but failed to send verification email. Please try signing in.';
					} else {
						step = 'verify';
						success = 'Account created! Please check your email for the verification code.';
					}
				}
			} else {
				// Verify email with OTP first
				const verifyResult = await authClient.emailOtp.verifyEmail({
					email,
					otp: otpCode
				});
				
				if (verifyResult.error) {
					error = verifyResult.error.message ?? 'Invalid verification code';
				} else {
					// Email verified, now sign in with email/password
					const signInResult = await signIn.email({
						email,
						password,
						rememberMe: true
					});
					
					if (signInResult.error) {
						error = 'Email verified but failed to sign in. Please try signing in manually.';
						setTimeout(() => goto('/signin'), 2000);
					} else {
						success = 'Email verified successfully! Signing you in...';
						// Redirect to app after successful verification and sign in
						setTimeout(() => goto('/app'), 1500);
					}
				}
			}
		} finally {
			loading = false;
		}
	}

	function resendOTP() {
		error = '';
		success = '';
		loading = true;
		
		authClient.emailOtp.sendVerificationOtp({
			email,
			type: 'email-verification'
		}).then(result => {
			if (result.error) {
				error = 'Failed to resend verification code';
			} else {
				success = 'Verification code sent again!';
			}
		}).finally(() => {
			loading = false;
		});
	}

	function backToSignup() {
		step = 'signup';
		otpCode = '';
		error = '';
		success = '';
	}
</script>

{#if step === 'signup'}
	<!-- Sign Up Form -->
	<form class="mx-auto mt-10 flex max-w-md flex-col gap-4" onsubmit={handleSubmit} novalidate>
		<h2 class="mb-4 text-2xl font-bold text-center">Create Account</h2>
		
		<!-- Hidden username field for accessibility -->
		<input
			type="email"
			name="username"
			value={email}
			autocomplete="username"
			style="display: none;"
			readonly
			tabindex="-1"
			aria-hidden="true"
		/>
		
		<input
			type="email"
			name="email"
			placeholder="Email"
			class="rounded-md border border-gray-300 p-2"
			bind:value={email}
			disabled={loading}
			autocomplete="email"
			required
		/>
		<input
			type="password"
			name="password"
			placeholder="Password (min 8 characters)"
			class="rounded-md border border-gray-300 p-2"
			bind:value={password}
			disabled={loading}
			autocomplete="new-password"
			required
			minlength="8"
		/>
		<button 
			type="submit" 
			class="rounded-md bg-blue-500 p-2 text-white disabled:opacity-50 disabled:cursor-not-allowed"
			disabled={loading}
			aria-describedby={error ? 'error-message' : undefined}
		>
			{loading ? 'Creating account...' : 'Sign up'}
		</button>
		
		<p class="mt-4 text-sm text-gray-500">
			Already have an account? <a href="/signin" class="text-blue-500">Sign in</a>
		</p>
	</form>
	{:else}
	<!-- Email Verification Form -->
	<form class="mx-auto mt-10 flex max-w-md flex-col gap-4" onsubmit={handleSubmit} novalidate>
		<h2 class="mb-4 text-2xl font-bold text-center">Verify Your Email</h2>
		
		<div class="rounded-md bg-blue-50 p-3 text-sm text-blue-800">
			📧 We've sent a verification code to <strong>{email}</strong>
		</div>
		
		<input
			type="text"
			name="otp"
			placeholder="Enter verification code"
			class="rounded-md border border-gray-300 p-2 text-center font-mono text-lg tracking-widest"
			bind:value={otpCode}
			disabled={loading}
			required
			maxlength="6"
			autocomplete="one-time-code"
		/>
		
		<button 
			type="submit" 
			class="rounded-md bg-green-500 p-2 text-white disabled:opacity-50 disabled:cursor-not-allowed"
			disabled={loading || !otpCode.trim()}
			aria-describedby={error ? 'error-message' : undefined}
		>
			{loading ? 'Verifying...' : 'Verify Email'}
		</button>
		
		<div class="flex justify-between text-sm">
			<button
				type="button"
				class="text-gray-500 hover:text-gray-700"
				onclick={backToSignup}
				disabled={loading}
			>
				← Back to signup
			</button>
			<button
				type="button"
				class="text-blue-500 hover:text-blue-700"
				onclick={resendOTP}
				disabled={loading}
			>
				Resend code
			</button>
		</div>
	</form>
	{/if}
	
	<!-- Success Messages -->
	{#if success}
		<div class="mt-4 rounded-md bg-green-50 p-3 text-sm text-green-800">
			{success}
		</div>
	{/if}
	
	<!-- Error Messages -->
	{#if error}
		<div id="error-message" class="mt-4 rounded-md bg-red-50 border border-red-200 p-3 text-sm text-red-700" role="alert">
			{error}
		</div>
	{/if}
