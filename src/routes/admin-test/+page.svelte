<script lang="ts">
	import { useSession } from '$lib/authClient';
	
	const session = useSession();
</script>

<div class="mx-auto max-w-2xl p-8">
	<h1 class="mb-6 text-3xl font-bold">Admin System Test</h1>
	
	{#if $session?.data}
		<div class="mb-6 rounded-lg bg-green-50 border border-green-200 p-4">
			<h2 class="mb-2 text-lg font-semibold text-green-800">✅ Authentication Working</h2>
			<div class="text-sm text-green-700">
				<p><strong>User ID:</strong> {$session.data.user.id}</p>
				<p><strong>Email:</strong> {$session.data.user.email}</p>
				<p><strong>Name:</strong> {$session.data.user.name}</p>
				<p><strong>Role:</strong> {($session.data.user as any).role ?? 'Not set'}</p>
				<p><strong>Email Verified:</strong> {$session.data.user.emailVerified ? 'Yes' : 'No'}</p>
			</div>
		</div>

		{#if ($session.data.user as any).role === 'admin'}
			<div class="mb-6 rounded-lg bg-blue-50 border border-blue-200 p-4">
				<h2 class="mb-2 text-lg font-semibold text-blue-800">🔑 Admin Access Confirmed</h2>
				<p class="text-sm text-blue-700">
					You have admin privileges and can access all CMS functionality.
				</p>
			</div>
			
			<div class="space-y-3">
				<a 
					href="/app" 
					class="block rounded-md bg-blue-600 px-4 py-2 text-center text-white hover:bg-blue-700"
				>
					Go to Admin Dashboard
				</a>
				<a 
					href="/app/add" 
					class="block rounded-md border border-blue-600 px-4 py-2 text-center text-blue-600 hover:bg-blue-50"
				>
					Add Website
				</a>
				<a 
					href="/app/manage" 
					class="block rounded-md border border-blue-600 px-4 py-2 text-center text-blue-600 hover:bg-blue-50"
				>
					Manage Websites
				</a>
			</div>
		{:else}
			<div class="mb-6 rounded-lg bg-red-50 border border-red-200 p-4">
				<h2 class="mb-2 text-lg font-semibold text-red-800">❌ No Admin Access</h2>
				<p class="text-sm text-red-700">
					Your account role is "{($session.data.user as any).role ?? 'Not set'}" but admin access requires "admin" role.
				</p>
			</div>
		{/if}
	{:else}
		<div class="mb-6 rounded-lg bg-yellow-50 border border-yellow-200 p-4">
			<h2 class="mb-2 text-lg font-semibold text-yellow-800">⚠️ Not Authenticated</h2>
			<p class="text-sm text-yellow-700 mb-3">
				You need to sign in to test the admin system.
			</p>
			<div class="space-y-2">
				<a 
					href="/signin" 
					class="block rounded-md bg-yellow-600 px-4 py-2 text-center text-white hover:bg-yellow-700"
				>
					Sign In
				</a>
				<a 
					href="/signup" 
					class="block rounded-md border border-yellow-600 px-4 py-2 text-center text-yellow-600 hover:bg-yellow-50"
				>
					Create Admin Account
				</a>
			</div>
		</div>
	{/if}
	
	<div class="mt-8 rounded-lg bg-gray-50 border border-gray-200 p-4">
		<h2 class="mb-2 text-lg font-semibold text-gray-800">📋 Setup Checklist</h2>
		<ul class="text-sm text-gray-700 space-y-1">
			<li>✅ Database migration generated and ready</li>
			<li>⚠️ Update <code>ADMIN_EMAILS</code> in your <code>.env</code> file</li>
			<li>🔄 Run <code>bun run db:migrate</code> to apply schema changes</li>
			<li>🔄 Restart dev server after email change</li>
			<li>📧 Test signup with your admin email</li>
			<li>🔐 Verify OTP code (check console)</li>
			<li>🎯 Access admin dashboard at <code>/app</code></li>
		</ul>
	</div>
	
	<div class="mt-6 text-center">
		<a href="/" class="text-blue-600 hover:underline">← Back to Home</a>
	</div>
</div>
