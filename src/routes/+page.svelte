<script>
	let { data } = $props();
</script>

<div class="container mx-auto px-4 py-8">
	<h1 class="mb-8 text-4xl font-bold">Tailwind Gallery</h1>
	
	{#if data.websites.length === 0}
		<p class="text-gray-600">No websites published yet. Be the first to add one!</p>
	{:else}
		<div class="grid gap-6 sm:grid-cols-2 lg:grid-cols-3">
			{#each data.websites as website}
				<div class="overflow-hidden rounded-lg border border-gray-200 shadow-sm hover:shadow-md transition-shadow">
					<img
						src={website.thumbnail}
						alt={website.title}
						class="h-48 w-full object-cover"
					/>
					<div class="p-4">
						<h3 class="mb-2 text-lg font-semibold">{website.title}</h3>
						<p class="text-sm text-gray-600">
							Tailwind CSS {website.tailwindVersion}
						</p>
					</div>
				</div>
			{/each}
		</div>
	{/if}
</div>
