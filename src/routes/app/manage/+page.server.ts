import { fail } from '@sveltejs/kit';
import { db } from '$lib/server/db';
import { websites } from '$lib/server/db/schema';
import { eq, and } from 'drizzle-orm';
import type { Actions, PageServerLoad } from './$types';

export const load = (async (event) => {
	// Auth is guaranteed by the hook and stored in locals
	const { auth } = event.locals;
	if (import.meta.env.DEV) console.assert(auth, 'Auth should be guaranteed by hook for /app routes');

	// Fetch user's websites
	const userWebsites = await db
		.select()
		.from(websites)
		.where(eq(websites.userId, auth!.user.id))
		.orderBy(websites.createdAt);

	return {
		websites: userWebsites
	};
}) satisfies PageServerLoad;

export const actions = {
	toggleStatus: async (event) => {
		// Auth is guaranteed by the hook and stored in locals
		const { auth } = event.locals;
		if (import.meta.env.DEV) console.assert(auth, 'Auth should be guaranteed by hook for /app routes');

		const data = await event.request.formData();
		const id = data.get('id') as string;
		const currentStatus = data.get('currentStatus') as string;

		try {
			// Toggle status
			const newStatus = currentStatus === 'published' ? 'draft' : 'published';
			await db
				.update(websites)
				.set({
					status: newStatus,
					publishedAt: newStatus === 'published' ? new Date() : null,
					updatedAt: new Date()
				})
				.where(
					and(
						eq(websites.id, id),
						eq(websites.userId, auth!.user.id) // Ensure user owns the website
					)
				);

			return { success: true };
		} catch (error) {
			console.error('Error toggling status:', error);
			return fail(500, { error: 'Failed to update status' });
		}
	}
} satisfies Actions; 