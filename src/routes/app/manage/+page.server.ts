import { fail } from '@sveltejs/kit';
import { db } from '$lib/server/db';
import { websites } from '$lib/server/db/schema';
import { auth } from '$lib/auth';
import { eq, and } from 'drizzle-orm';
import type { Actions, PageServerLoad } from './$types';

export const load = (async (event) => {
	// Session is guaranteed by the hook - no need to check again
	const session = await auth.api.getSession({ headers: event.request.headers });

	// Fetch user's websites
	const userWebsites = await db
		.select()
		.from(websites)
		.where(eq(websites.userId, session!.user.id))
		.orderBy(websites.createdAt);

	return {
		websites: userWebsites
	};
}) satisfies PageServerLoad;

export const actions = {
	toggleStatus: async (event) => {
		// Session is guaranteed by the hook - no need to check again
		const session = await auth.api.getSession({ headers: event.request.headers });

		const data = await event.request.formData();
		const id = data.get('id') as string;
		const currentStatus = data.get('currentStatus') as string;

		try {
			// Toggle status
			const newStatus = currentStatus === 'published' ? 'draft' : 'published';
			await db
				.update(websites)
				.set({
					status: newStatus,
					publishedAt: newStatus === 'published' ? new Date() : null,
					updatedAt: new Date()
				})
				.where(
					and(
						eq(websites.id, id),
						eq(websites.userId, session!.user.id) // Ensure user owns the website
					)
				);

			return { success: true };
		} catch (error) {
			console.error('Error toggling status:', error);
			return fail(500, { error: 'Failed to update status' });
		}
	}
} satisfies Actions; 