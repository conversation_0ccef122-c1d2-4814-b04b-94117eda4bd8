<script>
	import { enhance } from '$app/forms';
	
	let { data } = $props();
</script>

<div class="container mx-auto px-4 py-8">
	<div class="mb-6 flex items-center justify-between">
		<h1 class="text-2xl font-bold">Manage Websites</h1>
		<a
			href="/app/add"
			class="rounded-md bg-blue-600 px-4 py-2 text-white hover:bg-blue-700"
		>
			Add New Website
		</a>
	</div>
	
	{#if data.websites.length === 0}
		<p class="text-gray-600">No websites yet. Add your first one!</p>
	{:else}
		<div class="overflow-x-auto">
			<table class="w-full border-collapse">
				<thead>
					<tr class="border-b">
						<th class="p-2 text-left">Title</th>
						<th class="p-2 text-left">Version</th>
						<th class="p-2 text-left">Status</th>
						<th class="p-2 text-left">Added</th>
						<th class="p-2 text-left">Actions</th>
					</tr>
				</thead>
				<tbody>
					{#each data.websites as website}
						<tr class="border-b hover:bg-gray-50">
							<td class="p-2">{website.title}</td>
							<td class="p-2">{website.tailwindVersion}</td>
							<td class="p-2">
								<span class="rounded px-2 py-1 text-xs {website.status === 'published' ? 'bg-green-100 text-green-800' : 'bg-gray-100 text-gray-800'}">
									{website.status}
								</span>
							</td>
							<td class="p-2 text-sm text-gray-600">
								{new Date(website.createdAt).toLocaleDateString()}
							</td>
							<td class="p-2">
								<form method="POST" action="?/toggleStatus" use:enhance class="inline">
									<input type="hidden" name="id" value={website.id} />
									<input type="hidden" name="currentStatus" value={website.status} />
									<button
										type="submit"
										class="text-sm text-blue-600 hover:underline"
									>
										{website.status === 'published' ? 'Unpublish' : 'Publish'}
									</button>
								</form>
							</td>
						</tr>
					{/each}
				</tbody>
			</table>
		</div>
	{/if}
</div> 