<script lang="ts">
	import { goto } from '$app/navigation';
	import { useSession, signOut } from '$lib/authClient';

	const session = useSession();
	let signingOut = $state(false);

	const handleSignOut = async () => {
		signingOut = true;
		await signOut({
			fetchOptions: {
				onSuccess: () => {
					goto('/', { invalidateAll: true });
				},
				onError: () => {
					signingOut = false;
				}
			}
		});
	};
</script>

<div class="mx-auto max-w-4xl p-8">
	<div class="mb-8 flex items-center justify-between">
		<h1 class="text-3xl font-bold">Dashboard</h1>
		<button
	onclick={handleSignOut}
	disabled={signingOut}
	class="rounded-md bg-gray-500 px-4 py-2 text-white hover:bg-gray-600 disabled:opacity-50"
>
	{signingOut ? 'Signing out...' : 'Sign Out'}
</button>
	</div>
	
	<p class="mb-8 text-lg text-gray-600">Welcome to TailwindGallery.com Admin!</p>
	
	<div class="grid gap-6 md:grid-cols-2">
		<a
			href="/app/add"
			class="block rounded-lg border-2 border-blue-200 bg-blue-50 p-6 hover:border-blue-300 hover:bg-blue-100 transition-colors"
		>
			<h2 class="mb-2 text-xl font-semibold text-blue-900">Add Website</h2>
			<p class="text-blue-700">Submit a new website to the gallery</p>
		</a>
		
		<a
			href="/app/manage"
			class="block rounded-lg border-2 border-green-200 bg-green-50 p-6 hover:border-green-300 hover:bg-green-100 transition-colors"
		>
			<h2 class="mb-2 text-xl font-semibold text-green-900">Manage Websites</h2>
			<p class="text-green-700">View and manage your submitted websites</p>
		</a>
	</div>
	
	<div class="mt-8">
		<a href="/" class="text-blue-600 hover:underline">← View Public Gallery</a>
	</div>
</div>
