import { fail } from '@sveltejs/kit';
import { db } from '$lib/server/db';
import { websites } from '$lib/server/db/schema';
import type { Actions } from './$types';

export const actions = {
	default: async (event) => {
		// Session is guaranteed by the hook and stored in locals
		const { session } = event.locals;
		console.assert(session, 'Session should be guaranteed by hook for /app routes');

		const data = await event.request.formData();
		const title = data.get('title') as string;
		const tailwindVersion = data.get('tailwindVersion') as string;
		const thumbnail = data.get('thumbnail') as string;
		const status = data.get('status') as string;

		// Basic validation
		if (!title || !tailwindVersion || !thumbnail) {
			return fail(400, { error: 'All fields are required' });
		}

		try {
			// Insert website
			await db.insert(websites).values({
				userId: session!.user.id,
				title,
				tailwindVersion,
				thumbnail,
				status: status as 'draft' | 'published',
				publishedAt: status === 'published' ? new Date() : null
			});

			return { success: true };
		} catch (error) {
			console.error('Error adding website:', error);
			return fail(500, { error: 'Failed to add website' });
		}
	}
} satisfies Actions; 