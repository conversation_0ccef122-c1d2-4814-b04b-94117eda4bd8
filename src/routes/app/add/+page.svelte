<script>
	import { enhance } from '$app/forms';
	
	let { form } = $props();
</script>

<div class="mx-auto max-w-md p-6">
	<h1 class="mb-6 text-2xl font-bold">Add Website</h1>
	
	{#if form?.error}
		<div class="mb-4 rounded bg-red-50 p-4 text-red-600">
			{form.error}
		</div>
	{/if}
	
	{#if form?.success}
		<div class="mb-4 rounded bg-green-50 p-4 text-green-600">
			Website added successfully!
		</div>
	{/if}
	
	<form method="POST" use:enhance class="space-y-4">
		<div>
			<label for="title" class="mb-1 block text-sm font-medium">
				Website Title
			</label>
			<input
				type="text"
				id="title"
				name="title"
				required
				class="w-full rounded-md border border-gray-300 px-3 py-2 focus:border-blue-500 focus:outline-none"
				placeholder="My Awesome Site"
			/>
		</div>
		
		<div>
			<label for="tailwindVersion" class="mb-1 block text-sm font-medium">
				Tailwind Version
			</label>
			<select
				id="tailwindVersion"
				name="tailwindVersion"
				required
				class="w-full rounded-md border border-gray-300 px-3 py-2 focus:border-blue-500 focus:outline-none"
			>
				<option value="">Select version</option>
				<option value="3.4">Tailwind CSS 3.4</option>
				<option value="3.3">Tailwind CSS 3.3</option>
				<option value="3.2">Tailwind CSS 3.2</option>
				<option value="4.0">Tailwind CSS 4.0 (alpha)</option>
			</select>
		</div>
		
		<div>
			<label for="thumbnail" class="mb-1 block text-sm font-medium">
				Thumbnail URL
			</label>
			<input
				type="url"
				id="thumbnail"
				name="thumbnail"
				required
				class="w-full rounded-md border border-gray-300 px-3 py-2 focus:border-blue-500 focus:outline-none"
				placeholder="https://example.com/screenshot.png"
			/>
		</div>
		
		<div>
			<label for="status" class="mb-1 block text-sm font-medium">
				Status
			</label>
			<select
				id="status"
				name="status"
				class="w-full rounded-md border border-gray-300 px-3 py-2 focus:border-blue-500 focus:outline-none"
			>
				<option value="draft">Draft</option>
				<option value="published">Published</option>
			</select>
		</div>
		
		<button
			type="submit"
			class="w-full rounded-md bg-blue-600 px-4 py-2 text-white hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2"
		>
			Add Website
		</button>
	</form>
</div> 