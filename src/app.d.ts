// See https://svelte.dev/docs/kit/types#app.d.ts
// for information about these interfaces
declare global {
	namespace App {
		// interface Error {}
		interface Locals {
			session: {
				session: import('better-auth').Session;
				user: import('better-auth').User;
			} | null;
		}
		// interface PageData {}
		// interface PageState {}
		// interface Platform {}
	}
}

// Augment Better Auth types to include our custom role field
declare module 'better-auth' {
	interface User {
		role?: 'admin' | 'contributor' | 'expert' | 'advertiser';
	}
}

export {};
