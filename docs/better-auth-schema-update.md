# Better Auth Schema Update Guide

## When to use this guide:

*   Adding a new plugin to Better Auth.
*   Updating `better-auth` or its plugins to a newer version.
*   Adding custom fields to core tables (`user`, `session`, etc.) via `auth.ts` configuration.
*   Suspecting a table/column is missing or needs an update.

## Key Principle:

Your `auth.ts` (specifically `src/lib/auth.ts` in your project) is the source of truth for `better-auth`'s schema requirements. The CLI generates a reference schema based on *that* configuration.

## Step by step:

### 0. Preparation (If applicable)

*   **If adding/updating a plugin or custom fields:**
    *   Update your `src/lib/auth.ts` first (e.g., add the new plugin to the `plugins` array, or define `additionalFields` in `user` or `session` objects within your `betterAuth` configuration).
*   **If updating `better-auth` version:**
    *   Update `better-auth` and any related `@better-auth/plugin-name` packages in your `package.json`.
    *   Run your package manager's install command (e.g., `pnpm install`).
    *   Carefully review the official `better-auth` changelog or migration guide for that version for any specific instructions.

### 1. Generate Reference Schema with CLI

Ensure your `src/lib/auth.ts` reflects the LATEST desired state before running this.
```bash
bunx @better-auth/cli generate --output temp-auth-schema.ts
```
*   This `temp-auth-schema.ts` file will contain Drizzle schema code representing what `better-auth` (and its plugins, based on your `auth.ts`) expects.

### 2. Compare with Current Schema

Use a diff tool to see the changes between the generated reference and your actual schema:
```bash
diff temp-auth-schema.ts src/lib/server/db/schema.ts
```

### 3. Analyze Differences

Review the output of the `diff` command. Look for:

*   ✅ **New tables:** Entirely new table definitions (e.g., `twoFactor`, `apiKey`, or other plugin-specific tables).
*   ✅ **New columns:** New fields added to existing tables (e.g., `users.twoFactorEnabled`, `users.isAnonymous`, `sessions.activeOrganizationId`).
*   ✅ **Custom fields:** Columns corresponding to `additionalFields` you defined in your `auth.ts` for `user` or `session`.
*   ⚠️ **Data type changes:** Less common, but possible if a plugin or core `better-auth` changes its requirements.
*   ⚠️ **Removed tables/columns:** Very rare for `better-auth` itself unless a feature is deprecated. Be extra cautious.

### 4. Manually Adapt Your Schema (`src/lib/server/db/schema.ts`)

**CRITICAL: DO NOT blindly copy `temp-auth-schema.ts` over your existing `schema.ts`.**
Manually integrate the identified changes, preserving your project's specific conventions (like UUIDs with `defaultRandom()`, specific `onDelete` rules for foreign keys, custom helper objects like `baseFields`).

```typescript
// Example: temp-auth-schema.ts (CLI Drizzle output) might show:
// export const users = pgTable('users', {
//   id: text('id').primaryKey(),
//   newPluginField: boolean('new_plugin_field'),
//   customUserField: text('custom_user_field') 
//   // ... other core fields
// });
// export const newPluginTable = pgTable('new_plugin_table', { /* ... */ });

// ✅ In your src/lib/server/db/schema.ts, you adapt:
import { pgTable, uuid, text, timestamp, boolean } from 'drizzle-orm/pg-core';
// ... import your schema helpers if you use them ...

export const users = pgTable('users', {
  // Your existing fields, e.g.:
  id: uuid('id').primaryKey().defaultRandom(), // Keep your style
  name: text('name').notNull(),
  email: text('email').notNull().unique(),
  // ... other existing user fields ...

  // Manually add the new field identified from the diff:
  newPluginField: boolean('new_plugin_field').default(false), // Add new field from plugin, with a default
  customUserField: text('custom_user_field'),                 // Add your custom field
  
  createdAt: timestamp('created_at').notNull().defaultNow(), // Assuming these are part of your style
  updatedAt: timestamp('updated_at').notNull().defaultNow()
});

// If a new table is indicated by the diff:
export const newPluginTable = pgTable('new_plugin_table', {
  id: uuid('id').primaryKey().defaultRandom(), // Your ID style
  userId: uuid('user_id') // Example foreign key
    .notNull()
    .references(() => users.id, { onDelete: 'cascade' }), 
  // ... adapt other columns from temp-auth-schema.ts using your conventions ...
  createdAt: timestamp('created_at').notNull().defaultNow(),
  updatedAt: timestamp('updated_at').notNull().defaultNow()
});

// ... ensure all other tables (sessions, accounts, verifications, etc.) are also updated if diff shows changes.
```
*   Carefully merge new fields/tables.
*   Ensure your ID types (e.g., `uuid().defaultRandom()`) and foreign key constraints are consistent with your project style.
*   Verify table and column names match what `better-auth` (and its plugins) expect. The `temp-auth-schema.ts` is your primary guide here.

### 5. Add/Update Comment with Date & Context

In `src/lib/server/db/schema.ts`, update or add a comment block for traceability:
```typescript
// ====== BETTER AUTH CORE & PLUGIN TABLES ======
// Last checked/updated: YYYY-MM-DD
// Reason for update: Added [PluginName] plugin / Upgraded BA to vX.Y.Z / Added custom 'user.fieldName'
// BA CLI version used for reference: @better-auth/cli@X.Y.Z
// Current BA plugins in use: emailOTP, twoFactor, organization, etc.
```

### 6. Generate DB Migration with Drizzle Kit

```bash
bun db:generate # Or your equivalent script for `drizzle-kit generate`
```

### 7. Critically Review Migration SQL

Open the newly generated SQL file (e.g., `drizzle/XXXX_migration_name.sql`):
```bash
cat drizzle/XXXX_new_migration.sql # Adjust path as needed
```
*   **Ensure it only contains the changes you expect.**
*   Pay close attention to any `DROP TABLE` or `DROP COLUMN` statements.
*   Verify data type changes are correct.

### 8. Apply Migration (Locally First!)

```bash
bun db:migrate # Or your equivalent script for `drizzle-kit migrate`
```

### 9. Test Thoroughly

*   Test all relevant authentication flows (signup, signin, OTP, password reset, features of newly added/updated plugins).
*   Test data access and modification related to new/changed schema fields.
*   Check for any runtime errors in your application logs.

### 10. Clean Up Temporary Files

```bash
rm temp-auth-schema.ts
```

## Worked Example: Adding the 'twoFactor' Plugin

This example demonstrates applying the steps above when adding the `twoFactor` plugin.

1.  **Preparation (Step 0):** Add `twoFactor()` to the `plugins` array in `src/lib/auth.ts`.
    ```typescript
    // src/lib/auth.ts
    import { betterAuth } from 'better-auth';
    import { emailOTP, twoFactor } from 'better-auth/plugins'; // Assuming emailOTP was already there
    // ... other imports ...

    export const auth = betterAuth({
        // ... your existing config ...
        plugins: [
            emailOTP({ /* ... emailOTP config ... */ }),
            twoFactor() // Newly added plugin
        ]
    });
    ```

2.  **Generate & Compare (Steps 1-3):**
    Run `bunx @better-auth/cli generate --output temp-schema.ts`, then `diff temp-schema.ts src/lib/server/db/schema.ts`.
    The `diff` would highlight that `temp-schema.ts` now includes definitions for tables like `twoFactor` and new columns like `users.twoFactorEnabled`.
    A simplified snippet from `temp-schema.ts` might look like:
    ```typescript
    // temp-schema.ts (simplified Drizzle output from CLI)
    // export const users = pgTable('users', { /*...,*/ twoFactorEnabled: boolean('two_factor_enabled'), /*...*/ });
    // export const twoFactor = pgTable('two_factor', { id: text('id').primaryKey(), userId: text('user_id'), /*...*/ });
    // export const twoFactorBackupCodes = pgTable('two_factor_backup_codes', { /* ... */ });
    ```

3.  **Manually Adapt (Step 4):**
    Integrate these into your `src/lib/server/db/schema.ts`, applying your project's conventions (UUIDs, `defaultNow()`, foreign keys).
    ```typescript
    // src/lib/server/db/schema.ts
    export const users = pgTable('users', {
      id: uuid('id').primaryKey().defaultRandom(),
      // ... existing fields ...
      twoFactorEnabled: boolean('two_factor_enabled').default(false) // Added with a default
    });

    export const twoFactor = pgTable('two_factor', {
      id: uuid('id').primaryKey().defaultRandom(), // Your style
      userId: uuid('user_id')
        .notNull()
        .references(() => users.id, { onDelete: 'cascade' }),
      secret: text('secret'), // From temp-schema.ts, adapted
      // ... adapt other columns like backupCodes (if separate table or JSONB), createdAt, updatedAt ...
      createdAt: timestamp('created_at').notNull().defaultNow(),
      updatedAt: timestamp('updated_at').notNull().defaultNow()
    });
    
    // Similarly, add twoFactorBackupCodes table if generated by the CLI and adapt its columns
    // export const twoFactorBackupCodes = pgTable('two_factor_backup_codes', { ... });
    ```

4.  **Follow Steps 5-10:** Update schema comments, generate migration (`bun db:generate`), review the SQL, apply migration (`bun db:migrate`), test thoroughly, and remove `temp-schema.ts`.

## ⚠️ Important Rules (Reiteration)

1.  **Always BACKUP your database** before applying schema changes in production.
2.  **NEVER BLINDLY OVERWRITE** your `schema.ts` file with the CLI output. Manual adaptation is key.
3.  **ALWAYS REVIEW generated SQL migration scripts** before applying them.
4.  **TEST LOCALLY EXTENSIVELY** after any schema change and migration.
5.  **MAINTAIN CONSISTENCY** in your schema (e.g., UUIDs for IDs, naming conventions).

## 🚨 Troubleshooting:

*   **Migration Fails:**
    *   Use `bun db:studio` (or Drizzle Studio / `psql`) to inspect the current database state.
    *   Manually revert/fix problematic migrations if needed.
    *   Carefully check Drizzle Kit error messages for clues.
*   **Auth Doesn't Work Post-Update:**
    *   Verify all new tables/columns are correctly defined in `src/lib/server/db/schema.ts` and properly exported/imported if you have an `index.ts` for your schema.
    *   Double-check your `src/lib/auth.ts` configuration for typos or misconfigurations related to the new plugin or feature.
    *   Examine server logs for any errors from `better-auth` or Drizzle during auth operations.
*   **TypeScript Errors:**
    *   Ensure your Drizzle schema types are correctly inferred by your application code.
    *   If you're using a tool that generates types from your schema, re-run it.
*   **CLI `generate` Issues:**
    *   Confirm `src/lib/auth.ts` is correctly configured and has no syntax errors.
    *   The CLI might struggle with very complex import aliases in `auth.ts`. If issues persist, try temporarily simplifying imports to relative paths in `auth.ts` before running `generate`. (See `docs/concepts/cli.mdx` -> Common Issues from `better-auth` docs).