# Admin Setup Instructions

## 🎯 Quick Setup (2 minutes)

### 1. Configure Your Admin Email

Edit `.env` and replace the placeholder with your actual email:

```bash
# In .env file
ADMIN_EMAILS=<EMAIL>
# For multiple admins: ADMIN_EMAILS=<EMAIL>,<EMAIL>
```

### 2. Apply Database Migration

The migration has been generated. Apply it to your database:

```bash
bun run db:migrate
```

### 3. Test Admin Access

1. Go to `/signup` and create an account with your admin email
2. Complete email verification (check console for OTP code)
3. Access `/app` - you should see the admin dashboard

## 🔒 What Changed

### ✅ **Preserved** (Your valuable code is safe!)
- ✅ All email OTP verification logic
- ✅ Better Auth configuration 
- ✅ Existing auth routes and flows
- ✅ Website management functionality
- ✅ Database schema (just added one field)

### 🆕 **Added** (Minimal, focused changes)
- 🆕 `role` field in users table (defaults to 'admin')
- 🆕 Admin email whitelist in `adminConfig.ts`
- 🆕 Registration restriction to admin emails only
- 🆕 Role-based access control for `/app` routes
- 🆕 Unauthorized page for non-admin users

## 🚀 Future Extensibility

When you're ready to add more user types, simply:

1. **Add new emails to different roles:**
```typescript
// In adminConfig.ts
allowedEmails: {
	admin: ['<EMAIL>'],
	contributor: ['<EMAIL>'],
	expert: ['<EMAIL>'],
}
```

2. **Update role permissions:**
```typescript
permissions: {
	admin: { canManageWebsites: true, canPublishWebsites: true },
	contributor: { canManageWebsites: true, canPublishWebsites: false },
	expert: { canManageWebsites: true, canPublishWebsites: true },
}
```

3. **Extend access control:**
```typescript
// In hooks.server.ts
function canAccessRoute(route: string, userRole: string) {
	if (route.startsWith('/app/admin')) return userRole === 'admin';
	if (route.startsWith('/app/contribute')) return ['admin', 'contributor'].includes(userRole);
	// etc.
}
```

## 🎯 Current State: Single Admin Focus

Right now, the system is perfectly configured for your immediate need:
- **One admin account** (you)
- **Full CMS access** to manage websites
- **Email OTP verification** working perfectly
- **Clean, extensible architecture** for future growth

No scope creep, no wasted time - just what you need to ship your CMS! 🚀
