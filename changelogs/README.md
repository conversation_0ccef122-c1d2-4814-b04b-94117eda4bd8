# Changelog Guide

## Quick Start
```bash
bun run changelog              # Creates timestamped file with git info
bunx cursor <generated-file>    # AI fills the details
```

## Auto-captured
- Git commit (hash, message, files changed)
- Date-based path: `changelogs/2025/01/26/2025-01-26-001.md`

## Change Tags
`[FEAT]` `[FIX]` `[CONF]` `[DEPS]` `[PERF]` `[BREAKING]`

## AI Filling Checklist
1. Check shell history: `fc -l -20 | grep -E "(bun|npm|git|test|build)"`
2. Fill sections:
   - **Summary**: One-line what was done
   - **What/Why/How**: Include key commands used
   - **Code snippets**: Use `file:line` notation
   - **Notes**: Gotchas, dependencies

## Example Fill
```markdown
### [DEPS] Update all dependencies
**What:** Updated all packages to latest versions
**Why:** Security patches and new features
**How:** `bun update --latest` + `bunx npm-check-updates -u`

\`\`\`json:package.json:48-50
"svelte": "^5.33.6",
"typescript": "^5.8.3",
\`\`\`
```

## Optional: Git hook reminder
```bash
git config core.hooksPath .githooks
``` 