# 2025-05-30 #6

## Commit: 4527fae
feat(auth): add loading state to sign out button

Prevent multiple sign out attempts by disabling the button during operation and showing a loading state. Also handle error case by resetting the button state if sign out fails.

Files changed:
- src/routes/app/+page.svelte


## Summary
Added loading state to sign-out button with proper error handling and UX improvements

## Changes

### UX Enhancement: Sign-Out Loading State
**What:** Implemented loading state for sign-out button with disabled state during operation and error recovery
**Why:** Prevents multiple sign-out attempts and provides clear feedback to users during the authentication process
**How:** Added `$state` reactive variable, async handler with proper error handling, and conditional button text/disabled state

```svelte
// Sign-out implementation with loading state
let signingOut = $state(false);

const handleSignOut = async () => {
	signingOut = true;
	await signOut({
		fetchOptions: {
			onSuccess: () => {
				goto('/', { invalidateAll: true });
			},
			onError: () => {
				signingOut = false;
			}
		}
	});
};

// Button with loading state
<button
	onclick={handleSignOut}
	disabled={signingOut}
	class="rounded-md bg-gray-500 px-4 py-2 text-white hover:bg-gray-600 disabled:opacity-50"
>
	{signingOut ? 'Signing out...' : 'Sign Out'}
</button>
```

## Notes
- Uses Svelte 5 `$state` rune for reactive loading state
- Button is disabled during sign-out to prevent double-clicks
- Error handling resets the loading state if sign-out fails
- Visual feedback with opacity change and text update
- Follows Better Auth + SvelteKit patterns with proper callbacks

---
💡 **Tip**: Check shell history for recent commands:
- zsh: `fc -l -20 | grep -E "(bun|npm|git|test|build)"`
- bash: `history 20 | grep -E "(bun|npm|git|test|build)"`
