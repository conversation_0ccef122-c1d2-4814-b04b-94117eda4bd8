# 2025-05-30 #3

## Commit: fb76a31
feat(auth): improve signup/signin forms with loading states and validation

- Add loading states to prevent duplicate submissions and improve UX
- Add form validation with required fields and minimum password length
- Format email for name field during signup by cleaning special characters
- Add disabled states for inputs during submission
- Fix error handling by resetting error state before submission

Files changed:
- src/lib/auth.ts
- src/routes/(auth)/signin/+page.svelte
- src/routes/(auth)/signup/+page.svelte


## Summary
Fixed authentication forms by adding preventDefault and improving UX with loading states and validation

## Changes

### [FIX] Form Submission with preventDefault
**What:** Added `event.preventDefault()` to both signin and signup forms to fix page reload issue
**Why:** Svelte 5's colon-less event attributes (`onsubmit`) require manual prevention - forms were reloading the page instead of handling auth client-side
**How:** 
- Added `event: Event` parameter to handleSubmit functions
- Called `event.preventDefault()` at the start of each handler
- Fixed the "The model 'user' was not found" error by adding `usePlural: true` to auth config

### [FEAT] Loading States and Better UX
**What:** Implemented loading states, input validation, and improved error handling
**Why:** Prevent duplicate submissions, provide user feedback, and ensure data quality
**How:** 
- Added `loading` state variable using `$state(false)`
- Disabled all inputs and buttons during submission
- Added dynamic button text: "Signing in..." / "Creating account..."
- Added `required` attributes and `minlength="8"` for password

```typescript:src/routes/(auth)/signin/+page.svelte:9-30
async function handleSubmit(event: Event) {
    event.preventDefault();
    error = '';
    loading = true;
    
    try {
        const result = await signIn.email({
            email,
            password,
            rememberMe: true,
            callbackURL: '/app'
        });
        
        if (result.error) {
            error = result.error.message ?? 'An unknown error occurred';
        } else {
            goto(result.data?.url ?? '/app');
        }
    } finally {
        loading = false;
    }
}
```

### [FEAT] Improved Name Extraction
**What:** Better name handling in signup form
**Why:** Simple email split was fragile - "<EMAIL>" became "john"
**How:** Enhanced with regex to handle dots, underscores, hyphens:

```typescript:src/routes/(auth)/signup/+page.svelte:17
name: email.split('@')[0].replace(/[._-]/g, ' ').trim()
```

## Notes
- **Svelte 5 Event Handling**: Remember that `onsubmit={handler}` requires manual `preventDefault()`, unlike the legacy `on:submit|preventDefault={handler}` syntax
- **Auth Config Fix**: The `usePlural: true` in auth.ts was crucial - Better Auth was looking for "user" table but ours is named "users"
- **Future Enhancement**: Consider adding password strength indicator or more sophisticated validation
- **Working Auth Flow**: Registration and signin now work properly without page reloads

---
💡 **Tip**: Check shell history for recent commands:
- zsh: `fc -l -20 | grep -E "(bun|npm|git|test|build)"`
- bash: `history 20 | grep -E "(bun|npm|git|test|build)"`
